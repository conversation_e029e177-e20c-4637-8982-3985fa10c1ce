"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingExecutionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingExecutionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browserless.ts":
/*!********************************!*\
  !*** ./src/lib/browserless.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browserless.io API service with key rotation\n// Similar to Jina API key rotation system\nclass BrowserlessService {\n    constructor(){\n        this.apiKeys = [];\n        this.currentKeyIndex = 0;\n        this.keyUsageCount = new Map();\n        this.keyErrors = new Map();\n        this.MAX_RETRIES = 3;\n        this.ERROR_THRESHOLD = 5;\n        this.ENDPOINT = 'https://production-sfo.browserless.io';\n        this.initializeKeys();\n        this.testConnectivity();\n    }\n    static getInstance() {\n        if (!BrowserlessService.instance) {\n            BrowserlessService.instance = new BrowserlessService();\n        }\n        return BrowserlessService.instance;\n    }\n    initializeKeys() {\n        // Load all Browserless API keys from environment (similar to Jina pattern)\n        this.apiKeys = [\n            process.env.BROWSERLESS_API_KEY,\n            process.env.BROWSERLESS_API_KEY_2,\n            process.env.BROWSERLESS_API_KEY_3,\n            process.env.BROWSERLESS_API_KEY_4,\n            process.env.BROWSERLESS_API_KEY_5,\n            process.env.BROWSERLESS_API_KEY_6,\n            process.env.BROWSERLESS_API_KEY_7,\n            process.env.BROWSERLESS_API_KEY_8,\n            process.env.BROWSERLESS_API_KEY_9,\n            process.env.BROWSERLESS_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            console.error('No Browserless API keys found in environment variables');\n            return;\n        }\n        console.log(`[Browserless] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage tracking\n        this.apiKeys.forEach((key)=>{\n            this.keyUsageCount.set(key, 0);\n            this.keyErrors.set(key, 0);\n        });\n    }\n    /**\n   * Test connectivity to Browserless service\n   */ async testConnectivity() {\n        if (this.apiKeys.length === 0) {\n            console.warn('[Browserless] No API keys available for connectivity test');\n            return;\n        }\n        try {\n            const testKey = this.apiKeys[0];\n            const testUrl = `${this.ENDPOINT}/function?token=${testKey}`;\n            // Simple connectivity test with proper function format\n            const testCode = `\n        export default async function ({ page }) {\n          return {\n            status: \"connectivity-test-success\",\n            timestamp: new Date().toISOString()\n          };\n        }\n      `;\n            const response = await fetch(testUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/javascript'\n                },\n                body: testCode,\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            if (response.ok) {\n                const result = await response.text();\n                console.log('[Browserless] ✅ Connectivity test successful:', result);\n            } else {\n                const errorText = await response.text();\n                console.warn(`[Browserless] ⚠️ Connectivity test failed with status: ${response.status}`);\n                console.warn(`[Browserless] Error details: ${errorText}`);\n                console.warn(`[Browserless] Using key: ${testKey.substring(0, 8)}...`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.warn(`[Browserless] ⚠️ Connectivity test failed: ${errorMessage}`);\n            // Check for specific network errors\n            if (errorMessage.includes('ENOTFOUND')) {\n                console.error('[Browserless] 🌐 DNS resolution failed - check if chrome.browserless.io is accessible');\n            } else if (errorMessage.includes('ECONNRESET')) {\n                console.error('[Browserless] 🔌 Connection reset - possible network or service issue');\n            }\n        }\n    }\n    getNextApiKey() {\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Browserless API keys available');\n        }\n        // Find the key with lowest usage and errors\n        let bestKey = this.apiKeys[0];\n        let bestScore = this.calculateKeyScore(bestKey);\n        for (const key of this.apiKeys){\n            const score = this.calculateKeyScore(key);\n            if (score < bestScore) {\n                bestKey = key;\n                bestScore = score;\n            }\n        }\n        return bestKey;\n    }\n    calculateKeyScore(key) {\n        const usage = this.keyUsageCount.get(key) || 0;\n        const errors = this.keyErrors.get(key) || 0;\n        // Higher score = worse key (more usage + more errors)\n        return usage + errors * 10;\n    }\n    incrementKeyUsage(key) {\n        const currentUsage = this.keyUsageCount.get(key) || 0;\n        this.keyUsageCount.set(key, currentUsage + 1);\n    }\n    incrementKeyError(key) {\n        const currentErrors = this.keyErrors.get(key) || 0;\n        this.keyErrors.set(key, currentErrors + 1);\n    }\n    isKeyHealthy(key) {\n        const errors = this.keyErrors.get(key) || 0;\n        return errors < this.ERROR_THRESHOLD;\n    }\n    getHealthyKeys() {\n        return this.apiKeys.filter((key)=>this.isKeyHealthy(key));\n    }\n    async executeFunction(code, context, config) {\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            // Reset error counts if all keys are unhealthy\n            this.keyErrors.clear();\n            this.apiKeys.forEach((key)=>this.keyErrors.set(key, 0));\n            console.log('All Browserless keys were unhealthy, resetting error counts');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const response = await this.makeRequest(apiKey, code, context, config);\n                // Success - return the response\n                return response;\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless attempt ${attempt + 1} failed:`, error);\n                // If it's a rate limit or quota error, mark the key as having an error\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless API attempts failed');\n    }\n    async makeRequest(apiKey, code, context, config) {\n        const url = `${this.ENDPOINT}/function?token=${apiKey}`;\n        const requestBody = context ? {\n            code,\n            context\n        } : code;\n        const headers = {\n            'Content-Type': context ? 'application/json' : 'application/javascript',\n            'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'\n        };\n        const response = await fetch(url, {\n            method: 'POST',\n            headers,\n            body: context ? JSON.stringify(requestBody) : code,\n            signal: AbortSignal.timeout(config?.timeout || 30000)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Browserless API error: ${response.status} - ${errorText}`);\n        }\n        const result = await response.json();\n        return result;\n    }\n    isRateLimitError(error) {\n        const message = error.message.toLowerCase();\n        return message.includes('rate limit') || message.includes('quota') || message.includes('429') || message.includes('too many requests');\n    }\n    // Convenience methods for common browser tasks\n    async navigateAndExtract(url, selector) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n        \n        const title = await page.title();\n        const content = ${selector ? `await page.$eval(\"${selector}\", el => el.textContent || el.innerText)` : 'await page.evaluate(() => document.body.innerText)'};\n        \n        return {\n          data: {\n            url: \"${url}\",\n            title,\n            content: content?.trim() || \"\"\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced BrowserQL automation for complex browsing tasks\n   * Supports form filling, CAPTCHA solving, multi-step workflows, and state-of-the-art browsing\n   */ async executeBrowserQLAutomation(automationScript, options = {}) {\n        const { timeout = 60000, humanLike = true, solveCaptcha = true, sessionId, screenshots = false } = options;\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            throw new Error('No healthy Browserless API keys available');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.maxRetries; attempt++){\n            try {\n                const currentKey = this.getNextApiKey();\n                console.log(`[Browserless] BrowserQL automation attempt ${attempt + 1} with key: ${currentKey.name}`);\n                // Build the endpoint URL\n                let endpoint = `https://production-sfo.browserless.io/chromium/bql?token=${currentKey.key}`;\n                if (sessionId) {\n                    endpoint += `&sessionId=${sessionId}`;\n                }\n                const response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'User-Agent': 'RouKey-Browser-Automation/1.0'\n                    },\n                    body: JSON.stringify({\n                        query: automationScript,\n                        variables: {\n                            timeout,\n                            humanLike,\n                            solveCaptcha,\n                            screenshots\n                        }\n                    }),\n                    signal: AbortSignal.timeout(timeout + 10000) // Add buffer to timeout\n                });\n                if (!response.ok) {\n                    throw new Error(`BrowserQL automation failed: ${response.status} ${response.statusText}`);\n                }\n                const result = await response.json();\n                if (result.errors && result.errors.length > 0) {\n                    throw new Error(`BrowserQL errors: ${result.errors.map((e)=>e.message).join(', ')}`);\n                }\n                console.log(`[Browserless] ✅ BrowserQL automation successful`);\n                return {\n                    data: result.data,\n                    type: \"application/json\",\n                    sessionId: result.sessionId,\n                    screenshots: result.screenshots\n                };\n            } catch (error) {\n                lastError = error;\n                console.error(`[Browserless] BrowserQL automation attempt ${attempt + 1} failed:`, error);\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All BrowserQL automation attempts failed');\n    }\n    /**\n   * Create a persistent browsing session for complex multi-step workflows\n   */ async createBrowsingSession(initialUrl, options = {}) {\n        const { timeout = 300000, humanLike = true, blockResources = [\n            '*.png',\n            '*.jpg',\n            '*.gif',\n            '*.mp4',\n            '*.css'\n        ] } = options;\n        const sessionScript = `\n      mutation CreateBrowsingSession {\n        ${blockResources.length > 0 ? `\n        setRequestInterception(enabled: true)\n        reject(patterns: ${JSON.stringify(blockResources)})\n        ` : ''}\n\n        goto(url: \"${initialUrl}\", waitUntil: networkIdle) {\n          status\n          time\n        }\n\n        ${humanLike ? `\n        # Add human-like behavior\n        waitForTimeout(time: ${Math.floor(Math.random() * 2000) + 1000}) {\n          time\n        }\n        ` : ''}\n\n        reconnect(timeout: ${timeout}) {\n          BrowserQLEndpoint\n        }\n      }\n    `;\n        const result = await this.executeBrowserQLAutomation(sessionScript, {\n            timeout\n        });\n        return {\n            sessionId: result.sessionId || 'default',\n            reconnectUrl: result.data.reconnect.BrowserQLEndpoint\n        };\n    }\n    /**\n   * Advanced form filling with human-like behavior\n   */ async fillFormAdvanced(sessionUrl, formData, submitSelector) {\n        const formScript = `\n      mutation FillFormAdvanced {\n        ${formData.map((field, index)=>{\n            const delay = field.delay || [\n                50,\n                150\n            ];\n            return `\n            field${index}: ${field.type === 'select' ? 'select' : field.type === 'checkbox' || field.type === 'radio' ? 'click' : 'type'}(\n              selector: \"${field.selector}\"\n              ${field.type !== 'checkbox' && field.type !== 'radio' ? `text: \"${field.value}\"` : ''}\n              ${field.type === 'text' || field.type === 'email' || field.type === 'password' ? `delay: [${delay[0]}, ${delay[1]}]` : ''}\n              visible: true\n            ) {\n              time\n              ${field.type === 'text' || field.type === 'email' || field.type === 'password' ? 'text' : 'x'}\n            }\n          `;\n        }).join('\\n')}\n\n        ${submitSelector ? `\n        submitForm: click(\n          selector: \"${submitSelector}\"\n          visible: true\n        ) {\n          time\n          x\n          y\n        }\n        ` : ''}\n\n        # Wait for any page changes after form submission\n        waitForTimeout(time: 2000) {\n          time\n        }\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, formScript);\n    }\n    /**\n   * Execute BrowserQL script with existing session\n   */ async executeBrowserQLWithSession(sessionUrl, script) {\n        try {\n            const response = await fetch(sessionUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    query: script\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`Session execution failed: ${response.status} ${response.statusText}`);\n            }\n            const result = await response.json();\n            if (result.errors && result.errors.length > 0) {\n                throw new Error(`Session errors: ${result.errors.map((e)=>e.message).join(', ')}`);\n            }\n            return result;\n        } catch (error) {\n            console.error('[Browserless] Session execution failed:', error);\n            throw error;\n        }\n    }\n    async searchAndExtractUnblocked(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const healthyKeys = this.getHealthyKeys();\n        if (healthyKeys.length === 0) {\n            throw new Error('No healthy Browserless API keys available');\n        }\n        let lastError = null;\n        for(let attempt = 0; attempt < this.MAX_RETRIES; attempt++){\n            try {\n                const apiKey = this.getNextApiKey();\n                this.incrementKeyUsage(apiKey);\n                const url = `${this.ENDPOINT}/unblock?token=${apiKey}`;\n                const requestBody = {\n                    url: searchUrl,\n                    content: true,\n                    browserWSEndpoint: false,\n                    cookies: false,\n                    screenshot: false,\n                    waitForSelector: {\n                        selector: 'h3, .g h3, .LC20lb, .b_algo h2',\n                        timeout: 10000\n                    }\n                };\n                const response = await fetch(url, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'User-Agent': 'RouKey-Browser-Agent/1.0'\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: AbortSignal.timeout(60000)\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`Browserless unblock API error: ${response.status} - ${errorText}`);\n                }\n                const result = await response.json();\n                if (result.content) {\n                    // Parse the HTML content to extract search results\n                    const searchResults = this.parseSearchResults(result.content, searchEngine, query);\n                    return {\n                        data: {\n                            query,\n                            searchEngine,\n                            results: searchResults,\n                            timestamp: new Date().toISOString(),\n                            debug: {\n                                pageTitle: 'Unblocked search',\n                                pageUrl: searchUrl,\n                                totalElements: searchResults.length,\n                                usedSelector: 'unblock-api',\n                                extractedCount: searchResults.length\n                            }\n                        },\n                        type: \"application/json\"\n                    };\n                } else {\n                    throw new Error('No content returned from unblock API');\n                }\n            } catch (error) {\n                lastError = error;\n                console.error(`Browserless unblock attempt ${attempt + 1} failed:`, error);\n                if (this.isRateLimitError(error)) {\n                    const currentKey = this.getNextApiKey();\n                    this.incrementKeyError(currentKey);\n                }\n            }\n        }\n        throw lastError || new Error('All Browserless unblock API attempts failed');\n    }\n    /**\n   * Handle infinite scroll and pagination automatically\n   */ async handleInfiniteScroll(sessionUrl, options = {}) {\n        const { maxScrolls = 10, scrollDelay = 2000, contentSelector = 'body', stopCondition = 'no-more-content' } = options;\n        const scrollScript = `\n      mutation HandleInfiniteScroll {\n        # Get initial content count\n        initialContent: text(selector: \"${contentSelector}\") {\n          text\n        }\n\n        # Perform scrolling with content monitoring\n        ${Array.from({\n            length: maxScrolls\n        }, (_, i)=>`\n          scroll${i}: scroll(\n            direction: down\n            distance: 1000\n          ) {\n            time\n          }\n\n          waitAfterScroll${i}: waitForTimeout(time: ${scrollDelay}) {\n            time\n          }\n\n          contentCheck${i}: text(selector: \"${contentSelector}\") {\n            text\n          }\n        `).join('\\n')}\n\n        # Get final content\n        finalContent: text(selector: \"${contentSelector}\") {\n          text\n        }\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, scrollScript);\n    }\n    /**\n   * Advanced content extraction with multiple strategies\n   */ async extractContentAdvanced(sessionUrl, extractionRules) {\n        const extractionScript = `\n      mutation ExtractContentAdvanced {\n        ${extractionRules.map((rule, index)=>{\n            if (rule.type === 'screenshot') {\n                return `\n              ${rule.name}: screenshot(\n                selector: \"${rule.selector}\"\n                fullPage: false\n              ) {\n                data\n              }\n            `;\n            } else if (rule.type === 'attribute') {\n                return `\n              ${rule.name}: html(\n                selector: \"${rule.selector}\"\n                visible: true\n              ) {\n                html\n              }\n            `;\n            } else {\n                return `\n              ${rule.name}: ${rule.type}(\n                selector: \"${rule.selector}\"\n                visible: true\n              ) {\n                ${rule.type}\n              }\n            `;\n            }\n        }).join('\\n')}\n      }\n    `;\n        return await this.executeBrowserQLWithSession(sessionUrl, extractionScript);\n    }\n    /**\n   * Complex multi-step automation workflow\n   */ async executeComplexWorkflow(initialUrl, workflow) {\n        // Create session first\n        const session = await this.createBrowsingSession(initialUrl, {\n            timeout: 600000,\n            humanLike: true\n        });\n        const workflowScript = `\n      mutation ComplexWorkflow {\n        ${workflow.map((step, index)=>{\n            switch(step.type){\n                case 'navigate':\n                    return `\n                step${index}_navigate: goto(\n                  url: \"${step.params.url}\"\n                  waitUntil: ${step.params.waitUntil || 'networkIdle'}\n                ) {\n                  status\n                  time\n                }\n              `;\n                case 'click':\n                    return `\n                step${index}_click: click(\n                  selector: \"${step.params.selector}\"\n                  visible: true\n                  ${step.params.timeout ? `timeout: ${step.params.timeout}` : ''}\n                ) {\n                  time\n                  x\n                  y\n                }\n              `;\n                case 'type':\n                    return `\n                step${index}_type: type(\n                  selector: \"${step.params.selector}\"\n                  text: \"${step.params.text}\"\n                  ${step.params.delay ? `delay: [${step.params.delay[0]}, ${step.params.delay[1]}]` : ''}\n                  visible: true\n                ) {\n                  time\n                  text\n                }\n              `;\n                case 'wait':\n                    return `\n                step${index}_wait: waitForTimeout(time: ${step.params.time || 1000}) {\n                  time\n                }\n              `;\n                case 'extract':\n                    return `\n                step${index}_extract: ${step.params.type || 'text'}(\n                  selector: \"${step.params.selector}\"\n                  visible: true\n                ) {\n                  ${step.params.type || 'text'}\n                }\n              `;\n                case 'scroll':\n                    return `\n                step${index}_scroll: scroll(\n                  direction: ${step.params.direction || 'down'}\n                  distance: ${step.params.distance || 500}\n                ) {\n                  time\n                }\n              `;\n                case 'captcha':\n                    return `\n                step${index}_captcha: verify(\n                  type: ${step.params.type || 'hcaptcha'}\n                ) {\n                  solved\n                  time\n                }\n              `;\n                case 'screenshot':\n                    return `\n                step${index}_screenshot: screenshot(\n                  ${step.params.selector ? `selector: \"${step.params.selector}\"` : ''}\n                  fullPage: ${step.params.fullPage || false}\n                ) {\n                  data\n                }\n              `;\n                default:\n                    return `# Unknown step type: ${step.type}`;\n            }\n        }).join('\\n')}\n\n        # Final status check\n        finalStatus: html(visible: false) {\n          html\n        }\n      }\n    `;\n        try {\n            const result = await this.executeBrowserQLWithSession(session.reconnectUrl, workflowScript);\n            return {\n                ...result,\n                sessionId: session.sessionId,\n                reconnectUrl: session.reconnectUrl\n            };\n        } catch (error) {\n            console.error('[Browserless] Complex workflow failed:', error);\n            throw error;\n        }\n    }\n    parseSearchResults(htmlContent, searchEngine, query) {\n        // Modern search result parsing with comprehensive snippet extraction\n        // Updated for 2025 Google/Bing HTML structures to provide accurate, current information\n        const results = [];\n        if (searchEngine === 'google') {\n            // Updated Google search result patterns for 2025\n            // Google now uses more dynamic class names and data attributes\n            const modernResultPatterns = [\n                // Main search result containers (2025 patterns)\n                /<div[^>]*data-ved=\"[^\"]*\"[^>]*class=\"[^\"]*g[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*MjjYud[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*kvH3mc[^\"]*\"[^>]*>(.*?)<\\/div>/gis,\n                /<div[^>]*class=\"[^\"]*N54PNb[^\"]*\"[^>]*>(.*?)<\\/div>/gis\n            ];\n            // Modern title patterns\n            const titlePatterns = [\n                /<h3[^>]*class=\"[^\"]*LC20lb[^\"]*\"[^>]*>([^<]+)<\\/h3>/i,\n                /<h3[^>]*class=\"[^\"]*DKV0Md[^\"]*\"[^>]*>([^<]+)<\\/h3>/i,\n                /<h3[^>]*>([^<]+)<\\/h3>/i\n            ];\n            // Modern link patterns\n            const linkPatterns = [\n                /<a[^>]*href=\"([^\"]*)\"[^>]*data-ved=\"[^\"]*\"[^>]*>/i,\n                /<a[^>]*href=\"([^\"]*)\"[^>]*>/i\n            ];\n            // Updated snippet patterns for 2025\n            const snippetPatterns = [\n                // Current Google snippet classes\n                /<span[^>]*class=\"[^\"]*aCOpRe[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                /<div[^>]*class=\"[^\"]*VwiC3b[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*class=\"[^\"]*st[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                /<div[^>]*data-content-feature=\"snippet\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*data-snippet=\"[^\"]*\"[^>]*>([^<]+)<\\/span>/i,\n                // Fallback patterns\n                /<div[^>]*class=\"[^\"]*IsZvec[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<span[^>]*class=\"[^\"]*hgKElc[^\"]*\"[^>]*>([^<]+)<\\/span>/i\n            ];\n            // Try each result container pattern\n            for (const containerPattern of modernResultPatterns){\n                let containerMatch;\n                while((containerMatch = containerPattern.exec(htmlContent)) !== null && results.length < 10){\n                    const containerContent = containerMatch[1];\n                    // Extract title using multiple patterns\n                    let title = '';\n                    for (const titlePattern of titlePatterns){\n                        const titleMatch = titlePattern.exec(containerContent);\n                        if (titleMatch) {\n                            title = titleMatch[1].trim();\n                            break;\n                        }\n                    }\n                    // Extract link using multiple patterns\n                    let link = '';\n                    for (const linkPattern of linkPatterns){\n                        const linkMatch = linkPattern.exec(containerContent);\n                        if (linkMatch) {\n                            link = linkMatch[1];\n                            break;\n                        }\n                    }\n                    if (title && link && link.startsWith('http') && !link.includes('google.com') && !link.includes('/search?') && !link.includes('webcache.googleusercontent.com')) {\n                        // Extract snippet using multiple patterns\n                        let snippet = '';\n                        for (const snippetPattern of snippetPatterns){\n                            const snippetMatch = snippetPattern.exec(containerContent);\n                            if (snippetMatch) {\n                                snippet = snippetMatch[1].trim();\n                                // Clean up HTML entities and normalize whitespace\n                                snippet = snippet.replace(/&[^;]+;/g, ' ').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                                break;\n                            }\n                        }\n                        // Enhanced fallback snippet extraction\n                        if (!snippet) {\n                            // Try to extract any meaningful text from the container\n                            const cleanText = containerContent.replace(/<script[^>]*>.*?<\\/script>/gis, '').replace(/<style[^>]*>.*?<\\/style>/gis, '').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                            const sentences = cleanText.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n                            if (sentences.length > 0) {\n                                snippet = sentences[0].trim().substring(0, 200) + '...';\n                            } else {\n                                const words = cleanText.split(' ').filter((w)=>w.length > 2);\n                                if (words.length > 10) {\n                                    snippet = words.slice(0, 25).join(' ') + '...';\n                                }\n                            }\n                        }\n                        results.push({\n                            title,\n                            link,\n                            snippet: snippet || 'No description available',\n                            searchEngine: 'google',\n                            query,\n                            timestamp: new Date().toISOString(),\n                            relevanceScore: snippet.length > 50 ? 0.9 : 0.7 // Higher score for better snippets\n                        });\n                    }\n                }\n                if (results.length > 0) break; // Stop if we found results with this pattern\n            }\n        } else {\n            // Updated Bing search result patterns for 2025\n            const bingContainerPatterns = [\n                /<li[^>]*class=\"[^\"]*b_algo[^\"]*\"[^>]*>(.*?)<\\/li>/gis,\n                /<div[^>]*class=\"[^\"]*b_algoheader[^\"]*\"[^>]*>(.*?)<\\/div>/gis\n            ];\n            const bingTitlePatterns = [\n                /<h2[^>]*><a[^>]*href=\"([^\"]*)\"[^>]*>([^<]+)<\\/a><\\/h2>/i,\n                /<a[^>]*href=\"([^\"]*)\"[^>]*><h2[^>]*>([^<]+)<\\/h2><\\/a>/i\n            ];\n            const bingSnippetPatterns = [\n                /<p[^>]*class=\"[^\"]*b_lineclamp[^\"]*\"[^>]*>([^<]+)<\\/p>/i,\n                /<div[^>]*class=\"[^\"]*b_caption[^\"]*\"[^>]*>([^<]+)<\\/div>/i,\n                /<p[^>]*>([^<]+)<\\/p>/i\n            ];\n            for (const containerPattern of bingContainerPatterns){\n                let containerMatch;\n                while((containerMatch = containerPattern.exec(htmlContent)) !== null && results.length < 10){\n                    const containerContent = containerMatch[1];\n                    let title = '', link = '';\n                    for (const titlePattern of bingTitlePatterns){\n                        const titleMatch = titlePattern.exec(containerContent);\n                        if (titleMatch) {\n                            link = titleMatch[1];\n                            title = titleMatch[2].trim();\n                            break;\n                        }\n                    }\n                    if (title && link) {\n                        let snippet = '';\n                        for (const snippetPattern of bingSnippetPatterns){\n                            const snippetMatch = snippetPattern.exec(containerContent);\n                            if (snippetMatch) {\n                                snippet = snippetMatch[1].trim().replace(/&[^;]+;/g, ' ').replace(/<[^>]*>/g, ' ').replace(/\\s+/g, ' ').trim();\n                                break;\n                            }\n                        }\n                        results.push({\n                            title,\n                            link,\n                            snippet: snippet || 'No description available',\n                            searchEngine: 'bing',\n                            query,\n                            timestamp: new Date().toISOString(),\n                            relevanceScore: snippet.length > 50 ? 0.9 : 0.7\n                        });\n                    }\n                }\n                if (results.length > 0) break;\n            }\n        }\n        // Sort results by relevance score (better snippets first)\n        results.sort((a, b)=>(b.relevanceScore || 0) - (a.relevanceScore || 0));\n        console.log(`[Browserless] Parsed ${results.length} search results with enhanced snippets for query: \"${query}\"`);\n        return results.slice(0, 8); // Return top 8 most relevant results\n    }\n    async searchAndExtract(query, searchEngine = 'google') {\n        const searchUrl = searchEngine === 'google' ? `https://www.google.com/search?q=${encodeURIComponent(query)}` : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;\n        const code = `\n      export default async function ({ page }) {\n        // Set a realistic user agent and headers\n        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');\n        await page.setExtraHTTPHeaders({\n          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n          'Accept-Language': 'en-US,en;q=0.5',\n          'Accept-Encoding': 'gzip, deflate',\n          'DNT': '1',\n          'Connection': 'keep-alive',\n          'Upgrade-Insecure-Requests': '1',\n        });\n\n        await page.goto(\"${searchUrl}\", { waitUntil: 'networkidle0' });\n\n        // Set up CAPTCHA solving\n        const cdp = await page.createCDPSession();\n\n        // Check for CAPTCHA and solve if found\n        let captchaFound = false;\n        cdp.on('Browserless.captchaFound', () => {\n          console.log('CAPTCHA detected on search page');\n          captchaFound = true;\n        });\n\n        // Wait a moment to see if CAPTCHA is detected\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        if (captchaFound) {\n          console.log('Attempting to solve CAPTCHA...');\n          try {\n            const { solved, error } = await cdp.send('Browserless.solveCaptcha');\n            console.log('CAPTCHA solving result:', { solved, error });\n\n            if (solved) {\n              console.log('CAPTCHA solved successfully');\n              // Wait for page to reload after CAPTCHA solving\n              await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 }).catch(() => {\n                console.log('No navigation after CAPTCHA solve, continuing...');\n              });\n            } else {\n              console.log('CAPTCHA solving failed:', error);\n            }\n          } catch (captchaError) {\n            console.log('CAPTCHA solving error:', captchaError);\n          }\n        }\n\n        // Wait for search results to load with multiple fallback selectors\n        let resultsLoaded = false;\n        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];\n        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];\n\n        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n\n        for (const selector of selectorsToTry) {\n          try {\n            await page.waitForSelector(selector, { timeout: 3000 });\n            resultsLoaded = true;\n            console.log('Found results with selector:', selector);\n            break;\n          } catch (e) {\n            console.log('Selector failed:', selector);\n            continue;\n          }\n        }\n\n        if (!resultsLoaded) {\n          // Give it one more chance with a longer timeout on the most common selector\n          try {\n            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });\n          } catch (e) {\n            console.log('All selectors failed, proceeding anyway...');\n          }\n        }\n\n        const results = await page.evaluate(() => {\n          console.log('Starting search results extraction...');\n\n          // Try multiple selectors for extracting results\n          const googleSelectors = [\n            '[data-ved] h3',\n            'h3',\n            '.g h3',\n            '.LC20lb',\n            '.DKV0Md',\n            '#search h3',\n            '.yuRUbf h3',\n            'a h3',\n            '[role=\"heading\"]'\n          ];\n          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];\n\n          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;\n          let elements = [];\n          let usedSelector = '';\n\n          // Debug: Log page content\n          console.log('Page title:', document.title);\n          console.log('Page URL:', window.location.href);\n          console.log('Page body preview:', document.body.innerText.substring(0, 500));\n\n          for (const selector of selectorsToTry) {\n            elements = document.querySelectorAll(selector);\n            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');\n            if (elements.length > 0) {\n              usedSelector = selector;\n              console.log('Found', elements.length, 'results with selector:', selector);\n              break;\n            }\n          }\n\n          // If no results found, try a more generic approach\n          if (elements.length === 0) {\n            console.log('No results with specific selectors, trying generic approach...');\n            // Try to find any links that look like search results\n            const allLinks = document.querySelectorAll('a[href*=\"/url?\"]');\n            console.log('Found', allLinks.length, 'Google result links');\n\n            if (allLinks.length > 0) {\n              elements = Array.from(allLinks).map(link => {\n                const h3 = link.querySelector('h3');\n                return h3 || link;\n              }).filter(el => el && el.textContent?.trim());\n              usedSelector = 'a[href*=\"/url?\"] h3 (fallback)';\n              console.log('Using fallback approach, found', elements.length, 'elements');\n            }\n          }\n\n          const extractedResults = Array.from(elements).slice(0, 5).map(el => {\n            const title = el.textContent?.trim() || '';\n            let link = '';\n\n            // Try to get the link\n            if (el.href) {\n              link = el.href;\n            } else {\n              const closestLink = el.closest('a');\n              if (closestLink) {\n                link = closestLink.href;\n              }\n            }\n\n            return { title, link };\n          }).filter(item => item.title && item.link);\n\n          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);\n\n          return {\n            results: extractedResults,\n            debug: {\n              pageTitle: document.title,\n              pageUrl: window.location.href,\n              totalElements: elements.length,\n              usedSelector: usedSelector || 'none',\n              extractedCount: extractedResults.length\n            }\n          };\n        });\n\n        return {\n          data: {\n            query: \"${query}\",\n            searchEngine: \"${searchEngine}\",\n            results: results.results,\n            timestamp: new Date().toISOString(),\n            debug: results.debug\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code, null, {\n            timeout: 60000 // Increase timeout for CAPTCHA solving\n        });\n    }\n    async takeScreenshot(url, options) {\n        const fullPage = options?.fullPage ?? false;\n        const selector = options?.selector || '';\n        const quality = options?.quality || 80;\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        let screenshot;\n        if (\"${selector}\") {\n          // Screenshot specific element\n          const element = await page.waitForSelector(\"${selector}\", { timeout: 10000 });\n          screenshot = await element.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n        } else {\n          // Screenshot full page or viewport\n          screenshot = await page.screenshot({\n            encoding: 'base64',\n            fullPage: ${fullPage},\n            type: 'png',\n            quality: ${quality}\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            screenshot: screenshot,\n            selector: \"${selector}\",\n            fullPage: ${fullPage},\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Advanced form filling with intelligent field detection\n   */ async fillForm(url, formData, options) {\n        const submitAfterFill = options?.submitAfterFill ?? false;\n        const waitForNavigation = options?.waitForNavigation ?? false;\n        const formSelector = options?.formSelector || 'form';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const formData = ${JSON.stringify(formData)};\n        const results = [];\n\n        // Wait for form to be present\n        await page.waitForSelector(\"${formSelector}\", { timeout: 10000 });\n\n        // Fill each field intelligently\n        for (const [fieldName, value] of Object.entries(formData)) {\n          try {\n            // Try multiple selector strategies\n            const selectors = [\n              \\`input[name=\"\\${fieldName}\"]\\`,\n              \\`input[id=\"\\${fieldName}\"]\\`,\n              \\`textarea[name=\"\\${fieldName}\"]\\`,\n              \\`select[name=\"\\${fieldName}\"]\\`,\n              \\`input[placeholder*=\"\\${fieldName}\"]\\`,\n              \\`input[aria-label*=\"\\${fieldName}\"]\\`,\n              \\`[data-testid=\"\\${fieldName}\"]\\`\n            ];\n\n            let filled = false;\n            for (const selector of selectors) {\n              const elements = await page.$$(selector);\n              if (elements.length > 0) {\n                const element = elements[0];\n                const tagName = await element.evaluate(el => el.tagName.toLowerCase());\n\n                if (tagName === 'select') {\n                  await element.selectOption(value.toString());\n                } else if (tagName === 'input') {\n                  const inputType = await element.getAttribute('type');\n                  if (inputType === 'checkbox' || inputType === 'radio') {\n                    if (value) await element.check();\n                  } else {\n                    await element.fill(value.toString());\n                  }\n                } else {\n                  await element.fill(value.toString());\n                }\n\n                results.push({\n                  field: fieldName,\n                  selector: selector,\n                  value: value,\n                  success: true\n                });\n                filled = true;\n                break;\n              }\n            }\n\n            if (!filled) {\n              results.push({\n                field: fieldName,\n                value: value,\n                success: false,\n                error: 'Field not found'\n              });\n            }\n          } catch (error) {\n            results.push({\n              field: fieldName,\n              value: value,\n              success: false,\n              error: error.message\n            });\n          }\n        }\n\n        let submitResult = null;\n        if (${submitAfterFill}) {\n          try {\n            const submitButton = await page.$('input[type=\"submit\"], button[type=\"submit\"], button:has-text(\"Submit\")');\n            if (submitButton) {\n              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}\n              submitResult = { success: true, message: 'Form submitted successfully' };\n            } else {\n              submitResult = { success: false, error: 'Submit button not found' };\n            }\n          } catch (error) {\n            submitResult = { success: false, error: error.message };\n          }\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            formFillResults: results,\n            submitResult: submitResult,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * CAPTCHA solving with multiple strategies\n   */ async solveCaptcha(url, captchaType = 'recaptcha') {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const captchaType = \"${captchaType}\";\n        let result = { success: false, type: captchaType };\n\n        try {\n          if (captchaType === 'recaptcha') {\n            // Look for reCAPTCHA\n            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');\n            if (recaptcha) {\n              // For now, we'll detect and report the presence\n              // In production, integrate with 2captcha or similar service\n              const sitekey = await recaptcha.getAttribute('data-sitekey');\n              result = {\n                success: false,\n                type: 'recaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'reCAPTCHA detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'hcaptcha') {\n            // Look for hCaptcha\n            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');\n            if (hcaptcha) {\n              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');\n              result = {\n                success: false,\n                type: 'hcaptcha',\n                detected: true,\n                sitekey: sitekey,\n                message: 'hCaptcha detected but solving not implemented yet'\n              };\n            }\n          } else if (captchaType === 'text') {\n            // Look for text-based CAPTCHA\n            const textCaptcha = await page.$('img[src*=\"captcha\"], img[alt*=\"captcha\"], .captcha-image');\n            if (textCaptcha) {\n              result = {\n                success: false,\n                type: 'text',\n                detected: true,\n                message: 'Text CAPTCHA detected but solving not implemented yet'\n              };\n            }\n          }\n\n          // If no CAPTCHA detected\n          if (!result.detected) {\n            result = {\n              success: true,\n              type: captchaType,\n              detected: false,\n              message: 'No CAPTCHA detected on page'\n            };\n          }\n        } catch (error) {\n          result = {\n            success: false,\n            type: captchaType,\n            error: error.message\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            captchaResult: result,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Execute custom JavaScript with advanced capabilities\n   */ async executeAdvancedScript(url, script, options) {\n        const waitForSelector = options?.waitForSelector || '';\n        const timeout = options?.timeout || 30000;\n        const returnType = options?.returnType || 'json';\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        ${waitForSelector ? `await page.waitForSelector(\"${waitForSelector}\", { timeout: ${timeout} });` : ''}\n\n        // Execute custom script\n        const scriptResult = await page.evaluate(() => {\n          ${script}\n        });\n\n        let finalResult = scriptResult;\n\n        if (\"${returnType}\" === 'screenshot') {\n          const screenshot = await page.screenshot({\n            encoding: 'base64',\n            type: 'png'\n          });\n          finalResult = {\n            scriptResult: scriptResult,\n            screenshot: screenshot\n          };\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            result: finalResult,\n            returnType: \"${returnType}\",\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    /**\n   * Smart content extraction with multiple strategies\n   */ async smartExtract(url, extractionGoals) {\n        const code = `\n      export default async function ({ page }) {\n        await page.goto(\"${url}\", { waitUntil: 'networkidle0' });\n\n        const goals = ${JSON.stringify(extractionGoals)};\n        const results = {};\n\n        // Common extraction patterns\n        const extractors = {\n          prices: () => {\n            const priceSelectors = [\n              '[class*=\"price\"]', '[id*=\"price\"]', '.cost', '.amount',\n              '[data-testid*=\"price\"]', '.currency', '[class*=\"dollar\"]'\n            ];\n            const prices = [];\n            priceSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const text = el.textContent?.trim();\n                if (text && /[$£€¥₹]|\\\\d+\\\\.\\\\d{2}/.test(text)) {\n                  prices.push({\n                    text: text,\n                    selector: selector,\n                    element: el.tagName\n                  });\n                }\n              });\n            });\n            return prices;\n          },\n\n          contact: () => {\n            const contactSelectors = [\n              '[href^=\"mailto:\"]', '[href^=\"tel:\"]', '.contact', '.email', '.phone'\n            ];\n            const contacts = [];\n            contactSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                contacts.push({\n                  text: el.textContent?.trim(),\n                  href: el.getAttribute('href'),\n                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'\n                });\n              });\n            });\n            return contacts;\n          },\n\n          products: () => {\n            const productSelectors = [\n              '.product', '[class*=\"product\"]', '.item', '[data-testid*=\"product\"]'\n            ];\n            const products = [];\n            productSelectors.forEach(selector => {\n              document.querySelectorAll(selector).forEach(el => {\n                const title = el.querySelector('h1, h2, h3, .title, [class*=\"title\"]')?.textContent?.trim();\n                const price = el.querySelector('[class*=\"price\"], .cost')?.textContent?.trim();\n                const image = el.querySelector('img')?.src;\n                if (title) {\n                  products.push({ title, price, image });\n                }\n              });\n            });\n            return products;\n          },\n\n          text: () => {\n            // Extract main content\n            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];\n            let content = '';\n            for (const selector of contentSelectors) {\n              const el = document.querySelector(selector);\n              if (el) {\n                content = el.textContent?.trim() || '';\n                break;\n              }\n            }\n            if (!content) {\n              content = document.body.textContent?.trim() || '';\n            }\n            return content.substring(0, 5000); // Limit to 5000 chars\n          },\n\n          links: () => {\n            const links = [];\n            document.querySelectorAll('a[href]').forEach(el => {\n              const href = el.getAttribute('href');\n              const text = el.textContent?.trim();\n              if (href && text && !href.startsWith('#')) {\n                links.push({\n                  url: new URL(href, window.location.href).href,\n                  text: text\n                });\n              }\n            });\n            return links.slice(0, 50); // Limit to 50 links\n          }\n        };\n\n        // Execute extractors based on goals\n        goals.forEach(goal => {\n          const goalLower = goal.toLowerCase();\n          if (goalLower.includes('price') || goalLower.includes('cost')) {\n            results.prices = extractors.prices();\n          }\n          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {\n            results.contact = extractors.contact();\n          }\n          if (goalLower.includes('product') || goalLower.includes('item')) {\n            results.products = extractors.products();\n          }\n          if (goalLower.includes('text') || goalLower.includes('content')) {\n            results.text = extractors.text();\n          }\n          if (goalLower.includes('link') || goalLower.includes('url')) {\n            results.links = extractors.links();\n          }\n        });\n\n        // If no specific goals, extract everything\n        if (goals.length === 0) {\n          Object.keys(extractors).forEach(key => {\n            results[key] = extractors[key]();\n          });\n        }\n\n        return {\n          data: {\n            url: \"${url}\",\n            extractionGoals: goals,\n            results: results,\n            timestamp: new Date().toISOString()\n          },\n          type: \"application/json\"\n        };\n      }\n    `;\n        return this.executeFunction(code);\n    }\n    // Get service statistics\n    getStats() {\n        return {\n            totalKeys: this.apiKeys.length,\n            healthyKeys: this.getHealthyKeys().length,\n            keyUsage: Object.fromEntries(this.keyUsageCount),\n            keyErrors: Object.fromEntries(this.keyErrors)\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowserlessService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browserless.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/BrowsingExecutionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingExecutionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingExecutionService: () => (/* binding */ BrowsingExecutionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n/* harmony import */ var _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SmartBrowsingExecutor */ \"(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\");\n// Browsing Execution Service - Handles browsing model selection with fallback support\n// Integrates with BrowserlessService for actual web browsing\n\n\nclass BrowsingExecutionService {\n    constructor(){\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n        this.smartBrowsingExecutor = _SmartBrowsingExecutor__WEBPACK_IMPORTED_MODULE_1__.SmartBrowsingExecutor.getInstance();\n    }\n    static getInstance() {\n        if (!BrowsingExecutionService.instance) {\n            BrowsingExecutionService.instance = new BrowsingExecutionService();\n        }\n        return BrowsingExecutionService.instance;\n    }\n    /**\n   * Execute browsing with model fallback support\n   * Now uses SmartBrowsingExecutor for intelligent, plan-based browsing\n   */ async executeBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery, useSmartBrowsing = true, progressCallback) {\n        try {\n            if (!browsingConfig.browsing_enabled) {\n                return {\n                    success: false,\n                    error: 'Browsing is not enabled for this configuration'\n                };\n            }\n            if (!browsingConfig.browsing_models || browsingConfig.browsing_models.length === 0) {\n                return {\n                    success: false,\n                    error: 'No browsing models configured'\n                };\n            }\n            console.log(`[Browsing Execution] Starting ${useSmartBrowsing ? 'SMART' : 'SIMPLE'} browsing`);\n            console.log(`[Browsing Execution] Query: \"${query}\", Type: ${browsingType}`);\n            // Use Smart Browsing for complex tasks\n            if (useSmartBrowsing) {\n                console.log(`[Browsing Execution] 🧠 Using Smart Browsing Executor`);\n                const smartResult = await this.smartBrowsingExecutor.executeSmartBrowsing(refinedQuery || query, browsingConfig, browsingType, progressCallback);\n                if (smartResult.success) {\n                    return {\n                        success: true,\n                        content: smartResult.content,\n                        modelUsed: browsingConfig.browsing_models[0]?.model || 'smart-browsing',\n                        providerUsed: browsingConfig.browsing_models[0]?.provider || 'smart-browsing',\n                        browsingData: smartResult.plan\n                    };\n                } else {\n                    // Check if we should fallback due to network issues\n                    if (smartResult.shouldFallback) {\n                        console.log(`[Browsing Execution] 🌐 Network issue detected, falling back to simple browsing: ${smartResult.error}`);\n                    } else {\n                        console.log(`[Browsing Execution] Smart browsing failed, falling back to simple browsing: ${smartResult.error}`);\n                    }\n                // Fall back to simple browsing\n                }\n            }\n            // Fallback to simple browsing (original logic)\n            console.log(`[Browsing Execution] 🔄 Using Simple Browsing (fallback)`);\n            return await this.executeSimpleBrowsing(query, browsingConfig, browsingType, refinedQuery);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Fatal error:', errorMessage);\n            return {\n                success: false,\n                error: `Browsing execution failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Execute simple browsing (original logic) as fallback\n   */ async executeSimpleBrowsing(query, browsingConfig, browsingType = 'search', refinedQuery) {\n        // Sort models by order for fallback\n        const sortedModels = [\n            ...browsingConfig.browsing_models\n        ].sort((a, b)=>a.order - b.order);\n        console.log(`[Simple Browsing] Starting with ${sortedModels.length} models configured`);\n        let lastError = null;\n        // Try each model in order (strict fallback pattern)\n        for (const model of sortedModels){\n            try {\n                console.log(`[Simple Browsing] Attempting with ${model.provider}/${model.model}`);\n                // First, perform the web browsing\n                const browsingResult = await this.performWebBrowsing(refinedQuery || query, browsingType);\n                if (!browsingResult.success) {\n                    throw new Error(browsingResult.error || 'Browsing failed');\n                }\n                console.log(`[Simple Browsing] ✅ Web browsing successful, got ${JSON.stringify(browsingResult.data).length} characters of data`);\n                // Then, use the AI model to process the browsing results\n                const aiResult = await this.processWithAI(query, browsingResult.data, model, browsingType);\n                if (aiResult.success) {\n                    console.log(`[Simple Browsing] ✅ Success with ${model.provider}/${model.model}`);\n                    return {\n                        success: true,\n                        content: aiResult.content,\n                        modelUsed: model.model,\n                        providerUsed: model.provider,\n                        browsingData: browsingResult.data\n                    };\n                } else {\n                    throw new Error(aiResult.error || 'AI processing failed');\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                lastError = errorMessage;\n                console.log(`[Simple Browsing] ❌ Failed with ${model.provider}/${model.model}: ${errorMessage}`);\n                continue;\n            }\n        }\n        // If we get here, all models failed\n        return {\n            success: false,\n            error: `All browsing models failed. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Perform web browsing using BrowserlessService\n   */ async performWebBrowsing(query, browsingType) {\n        try {\n            let result;\n            switch(browsingType){\n                case 'search':\n                    // Use search functionality\n                    result = await this.browserlessService.searchAndExtractUnblocked(query);\n                    break;\n                case 'navigate':\n                    // Try to extract URL from query or use as-is\n                    const urlMatch = query.match(/https?:\\/\\/[^\\s]+/);\n                    const url = urlMatch ? urlMatch[0] : query;\n                    result = await this.browserlessService.navigateAndExtract(url);\n                    break;\n                case 'extract':\n                    // Similar to navigate but with specific extraction\n                    const extractUrl = query.match(/https?:\\/\\/[^\\s]+/)?.[0] || query;\n                    result = await this.browserlessService.navigateAndExtract(extractUrl);\n                    break;\n                default:\n                    // Default to search\n                    result = await this.browserlessService.searchAndExtractUnblocked(query);\n            }\n            if (result && result.data) {\n                console.log(`[Browsing Execution] ✅ Web browsing successful, got ${JSON.stringify(result.data).length} characters of data`);\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No data returned from browsing'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Browsing Execution] Web browsing failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Process browsing results with AI model\n   */ async processWithAI(originalQuery, browsingData, model, browsingType) {\n        try {\n            const prompt = this.buildProcessingPrompt(originalQuery, browsingData, browsingType);\n            // Call the appropriate AI provider\n            const response = await this.callAIProvider(prompt, model);\n            if (response && response.content) {\n                return {\n                    success: true,\n                    content: response.content\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'No content returned from AI model'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build processing prompt for AI model\n   */ buildProcessingPrompt(originalQuery, browsingData, browsingType) {\n        const dataStr = JSON.stringify(browsingData, null, 2);\n        return `You are an AI assistant that processes web browsing results to answer user queries.\n\nUSER QUERY: \"${originalQuery}\"\nBROWSING TYPE: ${browsingType}\n\nWEB BROWSING RESULTS:\n${dataStr}\n\nINSTRUCTIONS:\n1. Analyze the browsing results carefully\n2. Extract relevant information that answers the user's query\n3. Provide a comprehensive, well-structured response\n4. If the browsing results don't contain relevant information, say so clearly\n5. Include specific details, numbers, dates, and facts from the browsing results\n6. Organize the information in a clear, readable format\n7. Cite sources when possible (URLs, website names, etc.)\n\nPlease provide a helpful response based on the browsing results:`;\n    }\n    /**\n   * Get the correct model ID for API calls (following RouKey's pattern)\n   * OpenRouter keeps full model ID, other providers strip the prefix\n   */ getEffectiveModelId(model) {\n        // For OpenRouter, return the full model ID\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        // For other providers, extract the model name after the prefix\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Call AI provider based on model configuration\n   */ async callAIProvider(prompt, model) {\n        try {\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            // Get the effective model ID following RouKey's pattern\n            const effectiveModelId = this.getEffectiveModelId(model);\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: model.temperature || 0.2,\n                        max_tokens: 2000\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider: ${model.provider}`);\n            }\n            console.log(`[Browsing AI] Calling ${model.provider} API with model ${effectiveModelId} (original: ${model.model})...`);\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000) // 30s timeout\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`[Browsing AI] API error: ${response.status} - ${errorText}`);\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(`[Browsing AI] Raw response:`, JSON.stringify(result, null, 2));\n            // Extract content based on provider response format\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                // For OpenAI-compatible APIs (including Google's OpenAI-compatible endpoint)\n                content = result.choices?.[0]?.message?.content;\n            }\n            console.log(`[Browsing AI] Extracted content length: ${content?.length || 0}`);\n            if (!content || content.trim().length === 0) {\n                console.error(`[Browsing AI] No content extracted from response. Full response:`, result);\n                return {\n                    error: 'No content returned from AI model - empty response'\n                };\n            }\n            return {\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`[Browsing AI] Error calling AI provider:`, errorMessage);\n            return {\n                error: errorMessage\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingExecutionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingExecutionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts":
/*!***************************************************!*\
  !*** ./src/lib/browsing/SmartBrowsingExecutor.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartBrowsingExecutor: () => (/* binding */ SmartBrowsingExecutor)\n/* harmony export */ });\n/* harmony import */ var _lib_browserless__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/browserless */ \"(rsc)/./src/lib/browserless.ts\");\n// Smart Browsing Executor - Intelligent plan-based browsing with todo list management\n// Handles complex browsing tasks by creating plans, executing subtasks, and updating progress\n\nclass SmartBrowsingExecutor {\n    constructor(){\n        this.activePlans = new Map();\n        this.browserlessService = _lib_browserless__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance();\n    }\n    static getInstance() {\n        if (!SmartBrowsingExecutor.instance) {\n            SmartBrowsingExecutor.instance = new SmartBrowsingExecutor();\n        }\n        return SmartBrowsingExecutor.instance;\n    }\n    /**\n   * Execute smart browsing with planning and todo list management\n   */ async executeSmartBrowsing(query, browsingConfig, browsingType = 'search', progressCallback) {\n        try {\n            console.log(`[Smart Browsing] 🎯 Starting intelligent browsing for: \"${query}\"`);\n            // Step 1: Create a browsing plan\n            const plan = await this.createBrowsingPlan(query, browsingType, browsingConfig);\n            this.activePlans.set(plan.id, plan);\n            console.log(`[Smart Browsing] 📋 Created plan with ${plan.subtasks.length} subtasks`);\n            this.logPlan(plan);\n            // Notify plan creation\n            progressCallback?.onPlanCreated?.(plan);\n            // Step 2: Execute the plan\n            const result = await this.executePlan(plan, browsingConfig, progressCallback);\n            if (result.success) {\n                console.log(`[Smart Browsing] ✅ Plan completed successfully`);\n                return {\n                    success: true,\n                    content: result.content,\n                    plan: plan\n                };\n            } else {\n                console.log(`[Smart Browsing] ❌ Plan failed: ${result.error}`);\n                // Check if this is a network connectivity issue\n                if (this.isNetworkError(result.error)) {\n                    console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                    return {\n                        success: false,\n                        error: `Network connectivity issue: ${result.error}. Falling back to simple browsing.`,\n                        plan: plan,\n                        shouldFallback: true\n                    };\n                }\n                return {\n                    success: false,\n                    error: result.error,\n                    plan: plan\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Fatal error:', errorMessage);\n            // Check if this is a network connectivity issue\n            if (this.isNetworkError(errorMessage)) {\n                console.log(`[Smart Browsing] 🌐 Network connectivity issue detected, recommending fallback to simple browsing`);\n                return {\n                    success: false,\n                    error: `Network connectivity issue: ${errorMessage}. Falling back to simple browsing.`,\n                    shouldFallback: true\n                };\n            }\n            return {\n                success: false,\n                error: `Smart browsing failed: ${errorMessage}`\n            };\n        }\n    }\n    /**\n   * Check if an error is related to network connectivity\n   */ isNetworkError(errorMessage) {\n        const networkErrorPatterns = [\n            'fetch failed',\n            'ECONNRESET',\n            'ENOTFOUND',\n            'ETIMEDOUT',\n            'ECONNREFUSED',\n            'Network request failed',\n            'Connection timeout',\n            'DNS resolution failed'\n        ];\n        return networkErrorPatterns.some((pattern)=>errorMessage.toLowerCase().includes(pattern.toLowerCase()));\n    }\n    /**\n   * Create an intelligent browsing plan based on the query\n   */ async createBrowsingPlan(query, browsingType, browsingConfig) {\n        const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Use AI to create a smart plan\n        const planningResult = await this.generatePlanWithAI(query, browsingType, browsingConfig);\n        const plan = {\n            id: planId,\n            originalQuery: query,\n            goal: planningResult.goal || `Find comprehensive information about: ${query}`,\n            subtasks: planningResult.subtasks || this.createFallbackPlan(query, browsingType),\n            status: 'planning',\n            progress: 0,\n            gatheredData: {},\n            visitedUrls: [],\n            searchQueries: [],\n            completedSubtasks: [],\n            failedSubtasks: [],\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        return plan;\n    }\n    /**\n   * Generate a browsing plan using AI\n   */ async generatePlanWithAI(query, browsingType, browsingConfig) {\n        try {\n            const model = browsingConfig.browsing_models[0]; // Use first available model for planning\n            if (!model) {\n                throw new Error('No browsing models available for planning');\n            }\n            const planningPrompt = this.buildPlanningPrompt(query, browsingType);\n            const aiResult = await this.callAIForPlanning(planningPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return this.parsePlanFromAI(aiResult.content, query);\n            } else {\n                console.warn('[Smart Browsing] AI planning failed, using fallback plan');\n                return {\n                    goal: `Find information about: ${query}`,\n                    subtasks: this.createFallbackPlan(query, browsingType)\n                };\n            }\n        } catch (error) {\n            console.warn('[Smart Browsing] AI planning error, using fallback:', error);\n            return {\n                goal: `Find information about: ${query}`,\n                subtasks: this.createFallbackPlan(query, browsingType)\n            };\n        }\n    }\n    /**\n   * Build a comprehensive planning prompt for AI\n   */ buildPlanningPrompt(query, browsingType) {\n        // Get current date and time for context\n        const now = new Date();\n        const currentDateTime = now.toLocaleString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            timeZoneName: 'short'\n        });\n        const currentYear = now.getFullYear();\n        const currentMonth = now.toLocaleString('en-US', {\n            month: 'long'\n        });\n        return `You are an expert web browsing strategist. Create a detailed browsing plan to thoroughly research this query: \"${query}\"\n\nCURRENT DATE & TIME: ${currentDateTime}\nCURRENT YEAR: ${currentYear}\nCURRENT MONTH: ${currentMonth}\n\nBROWSING TYPE: ${browsingType}\n\nIMPORTANT: When generating search terms and subtasks, consider the current date and time context:\n- For recent events, include \"${currentYear}\" in search terms\n- For current trends, use \"latest\", \"recent\", \"${currentMonth} ${currentYear}\"\n- For news queries, prioritize recent timeframes\n- For technology/AI topics, include current year for latest developments\n- For market/business queries, focus on current and recent data\n\nCreate a JSON response with this structure:\n{\n  \"goal\": \"Clear statement of what we want to achieve\",\n  \"subtasks\": [\n    {\n      \"id\": \"unique_id\",\n      \"type\": \"search|navigate|extract|analyze\",\n      \"description\": \"What this subtask does\",\n      \"query\": \"Specific search query or URL\",\n      \"priority\": 1-10,\n      \"searchTerms\": [\"alternative\", \"search\", \"terms\"],\n      \"expectedInfo\": \"What information we expect to find\"\n    }\n  ]\n}\n\nGUIDELINES:\n1. Start with broad searches, then get more specific\n2. Use multiple search strategies and terms with temporal context\n3. Include fact-checking and verification steps\n4. Plan for 3-7 subtasks maximum\n5. Make search terms diverse, comprehensive, and time-aware\n6. Consider different angles and perspectives\n7. Include analysis steps to synthesize information\n8. For \"navigate\" tasks, only use if you have specific URLs (https://...)\n9. Most tasks should be \"search\" type for better reliability\n10. ALWAYS include temporal keywords when relevant:\n    - For news: \"latest news\", \"recent updates\", \"${currentMonth} ${currentYear}\"\n    - For trends: \"current trends\", \"${currentYear} trends\"\n    - For technology: \"latest developments\", \"${currentYear} updates\"\n    - For data/statistics: \"current data\", \"recent statistics\", \"${currentYear} data\"\n\nCreate a smart, thorough, and temporally-aware plan:`;\n    }\n    /**\n   * Enhance query with temporal context for better search results\n   */ enhanceQueryWithTemporal(query, temporalKeywords) {\n        const currentYear = new Date().getFullYear();\n        const currentMonth = new Date().toLocaleString('en-US', {\n            month: 'long'\n        });\n        // Detect if query already has temporal context\n        const hasTemporalContext = /\\b(latest|recent|current|new|today|\\d{4}|now)\\b/i.test(query);\n        return {\n            primary: query,\n            temporal: hasTemporalContext ? query : `${query} ${currentYear} latest`,\n            alternatives: [\n                query,\n                `${query} information`,\n                `${query} details`,\n                hasTemporalContext ? `${query} overview` : `${query} ${currentYear}`\n            ],\n            recentTerms: [\n                `${query} recent`,\n                `${query} latest news`,\n                `${query} ${currentMonth} ${currentYear}`,\n                `${query} current trends`\n            ]\n        };\n    }\n    /**\n   * Create a fallback plan when AI planning fails\n   */ createFallbackPlan(query, browsingType) {\n        const baseId = Date.now();\n        const currentYear = new Date().getFullYear();\n        const currentMonth = new Date().toLocaleString('en-US', {\n            month: 'long'\n        });\n        // Add temporal context to search terms\n        const temporalKeywords = [\n            `${currentYear}`,\n            `latest`,\n            `recent`,\n            `${currentMonth} ${currentYear}`\n        ];\n        const enhancedQuery = this.enhanceQueryWithTemporal(query, temporalKeywords);\n        return [\n            {\n                id: `search_${baseId}_1`,\n                type: 'search',\n                description: 'Primary search for main topic',\n                query: enhancedQuery.primary,\n                status: 'pending',\n                priority: 10,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: enhancedQuery.alternatives,\n                expectedInfo: 'General information about the topic'\n            },\n            {\n                id: `search_${baseId}_2`,\n                type: 'search',\n                description: 'Secondary search with temporal context',\n                query: enhancedQuery.temporal,\n                status: 'pending',\n                priority: 8,\n                attempts: 0,\n                maxAttempts: 3,\n                searchTerms: enhancedQuery.recentTerms,\n                expectedInfo: 'Recent developments and current information'\n            },\n            {\n                id: `analyze_${baseId}`,\n                type: 'analyze',\n                description: 'Analyze and synthesize gathered information',\n                query: 'synthesize findings',\n                status: 'pending',\n                priority: 5,\n                attempts: 0,\n                maxAttempts: 1,\n                dependencies: [\n                    `search_${baseId}_1`,\n                    `search_${baseId}_2`\n                ],\n                expectedInfo: 'Comprehensive summary of findings'\n            }\n        ];\n    }\n    /**\n   * Execute the browsing plan step by step\n   */ async executePlan(plan, browsingConfig, progressCallback) {\n        plan.status = 'executing';\n        plan.updatedAt = new Date().toISOString();\n        console.log(`[Smart Browsing] 🚀 Starting plan execution`);\n        try {\n            // Execute subtasks in priority order, respecting dependencies\n            while(this.hasRemainingTasks(plan)){\n                const nextTask = this.getNextExecutableTask(plan);\n                if (!nextTask) {\n                    console.log(`[Smart Browsing] ⏸️ No executable tasks remaining, checking if plan is complete`);\n                    break;\n                }\n                console.log(`[Smart Browsing] 🔄 Executing subtask: ${nextTask.description}`);\n                plan.currentSubtask = nextTask.id;\n                nextTask.status = 'in_progress';\n                // Notify task started\n                progressCallback?.onTaskStarted?.(nextTask, plan);\n                progressCallback?.onStatusUpdate?.(`Executing: ${nextTask.description}`, plan);\n                const taskResult = await this.executeSubtask(nextTask, plan, browsingConfig);\n                if (taskResult.success) {\n                    nextTask.status = 'completed';\n                    nextTask.result = taskResult.data;\n                    plan.completedSubtasks.push(nextTask.id);\n                    console.log(`[Smart Browsing] ✅ Subtask completed: ${nextTask.description}`);\n                    // Notify task completed\n                    progressCallback?.onTaskCompleted?.(nextTask, plan);\n                } else {\n                    nextTask.attempts++;\n                    nextTask.error = taskResult.error;\n                    if (nextTask.attempts >= nextTask.maxAttempts) {\n                        nextTask.status = 'failed';\n                        plan.failedSubtasks.push(nextTask.id);\n                        console.log(`[Smart Browsing] ❌ Subtask failed permanently: ${nextTask.description}`);\n                        // Notify task failed\n                        progressCallback?.onTaskFailed?.(nextTask, plan);\n                    } else {\n                        nextTask.status = 'pending';\n                        console.log(`[Smart Browsing] 🔄 Subtask failed, will retry (${nextTask.attempts}/${nextTask.maxAttempts}): ${nextTask.description}`);\n                    }\n                }\n                // Update progress\n                plan.progress = this.calculateProgress(plan);\n                progressCallback?.onProgressUpdate?.(plan.progress, plan);\n                plan.updatedAt = new Date().toISOString();\n                this.logProgress(plan);\n            }\n            // Generate final result\n            const finalResult = await this.synthesizeFinalResult(plan, browsingConfig);\n            plan.finalResult = finalResult;\n            plan.status = 'completed';\n            plan.progress = 100;\n            // Notify plan completed\n            progressCallback?.onPlanCompleted?.(plan);\n            progressCallback?.onStatusUpdate?.('Browsing completed successfully!', plan);\n            return {\n                success: true,\n                content: finalResult\n            };\n        } catch (error) {\n            plan.status = 'failed';\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error('[Smart Browsing] Plan execution failed:', errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Check if there are remaining tasks to execute\n   */ hasRemainingTasks(plan) {\n        return plan.subtasks.some((task)=>task.status === 'pending' || task.status === 'in_progress');\n    }\n    /**\n   * Get the next executable task (highest priority, dependencies met)\n   */ getNextExecutableTask(plan) {\n        const executableTasks = plan.subtasks.filter((task)=>{\n            if (task.status !== 'pending') return false;\n            // Check if dependencies are met\n            if (task.dependencies && task.dependencies.length > 0) {\n                return task.dependencies.every((depId)=>plan.completedSubtasks.includes(depId));\n            }\n            return true;\n        });\n        // Sort by priority (highest first)\n        executableTasks.sort((a, b)=>b.priority - a.priority);\n        return executableTasks[0] || null;\n    }\n    /**\n   * Execute a single subtask\n   */ async executeSubtask(subtask, plan, browsingConfig) {\n        try {\n            switch(subtask.type){\n                case 'search':\n                    return await this.executeSearchSubtask(subtask, plan);\n                case 'navigate':\n                    return await this.executeNavigateSubtask(subtask, plan);\n                case 'extract':\n                    return await this.executeExtractSubtask(subtask, plan);\n                case 'analyze':\n                    return await this.executeAnalyzeSubtask(subtask, plan, browsingConfig);\n                default:\n                    throw new Error(`Unknown subtask type: ${subtask.type}`);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Execute a search subtask with advanced BrowserQL capabilities and smart query refinement\n   */ async executeSearchSubtask(subtask, plan) {\n        const searchTerms = [\n            subtask.query,\n            ...subtask.searchTerms || []\n        ];\n        let lastError = '';\n        let bestResults = null;\n        let bestScore = 0;\n        console.log(`[Smart Browsing] 🔍 Advanced search execution for: \"${subtask.query}\"`);\n        // Try different search terms with enhanced result evaluation\n        for (const searchTerm of searchTerms){\n            try {\n                console.log(`[Smart Browsing] 🔍 Searching with enhanced parsing: \"${searchTerm}\"`);\n                plan.searchQueries.push(searchTerm);\n                // Use the enhanced search with better snippet extraction\n                const result = await this.browserlessService.searchAndExtractUnblocked(searchTerm);\n                if (result.data && result.data.results && result.data.results.length > 0) {\n                    console.log(`[Smart Browsing] ✅ Found ${result.data.results.length} results with enhanced snippets for: \"${searchTerm}\"`);\n                    // Calculate result quality score based on snippet content and relevance\n                    const resultScore = this.calculateResultQuality(result.data.results, subtask.query);\n                    console.log(`[Smart Browsing] 📊 Result quality score: ${resultScore.toFixed(2)} for \"${searchTerm}\"`);\n                    // Store URLs we've found\n                    result.data.results.forEach((item)=>{\n                        if (item.link && !plan.visitedUrls.includes(item.link)) {\n                            plan.visitedUrls.push(item.link);\n                        }\n                    });\n                    // Keep track of best results\n                    if (resultScore > bestScore) {\n                        bestScore = resultScore;\n                        bestResults = result.data;\n                    }\n                    // If we found high-quality results, use them immediately\n                    if (resultScore > 0.8) {\n                        console.log(`[Smart Browsing] 🎯 High-quality results found (score: ${resultScore.toFixed(2)}), using immediately`);\n                        return {\n                            success: true,\n                            data: result.data\n                        };\n                    }\n                } else {\n                    lastError = `No results found for: \"${searchTerm}\"`;\n                    console.log(`[Smart Browsing] ⚠️ ${lastError}`);\n                }\n            } catch (error) {\n                lastError = error instanceof Error ? error.message : 'Search failed';\n                console.log(`[Smart Browsing] ❌ Search error for \"${searchTerm}\": ${lastError}`);\n            }\n        }\n        // Return best results if we found any\n        if (bestResults && bestScore > 0.3) {\n            console.log(`[Smart Browsing] ✅ Using best results with score: ${bestScore.toFixed(2)}`);\n            return {\n                success: true,\n                data: bestResults\n            };\n        }\n        return {\n            success: false,\n            error: `All search terms failed to find quality results. Last error: ${lastError}`\n        };\n    }\n    /**\n   * Calculate result quality score based on snippet content and relevance\n   */ calculateResultQuality(results, originalQuery) {\n        if (!results || results.length === 0) return 0;\n        let totalScore = 0;\n        const queryWords = originalQuery.toLowerCase().split(/\\s+/).filter((word)=>word.length > 2);\n        const currentYear = new Date().getFullYear().toString();\n        for (const result of results){\n            let score = 0;\n            const title = (result.title || '').toLowerCase();\n            const snippet = (result.snippet || '').toLowerCase();\n            const combinedText = `${title} ${snippet}`;\n            // Base score for having content\n            if (snippet && snippet !== 'no description available') {\n                score += 0.3;\n            }\n            // Relevance score based on query word matches\n            const matchedWords = queryWords.filter((word)=>combinedText.includes(word));\n            score += matchedWords.length / queryWords.length * 0.4;\n            // Bonus for current year (indicates recent/current information)\n            if (combinedText.includes(currentYear)) {\n                score += 0.2;\n            }\n            // Bonus for temporal keywords indicating current information\n            const temporalKeywords = [\n                'latest',\n                'recent',\n                'current',\n                'new',\n                '2025',\n                'updated'\n            ];\n            const temporalMatches = temporalKeywords.filter((keyword)=>combinedText.includes(keyword));\n            score += Math.min(temporalMatches.length * 0.1, 0.3);\n            // Penalty for very short snippets\n            if (snippet.length < 50) {\n                score *= 0.7;\n            }\n            // Bonus for longer, more informative snippets\n            if (snippet.length > 150) {\n                score += 0.1;\n            }\n            totalScore += score;\n        }\n        return Math.min(totalScore / results.length, 1.0);\n    }\n    /**\n   * Execute navigate subtask with advanced automation capabilities\n   */ async executeNavigateSubtask(subtask, plan) {\n        try {\n            const query = subtask.query;\n            console.log(`[Smart Browsing] 🌐 Processing advanced navigation task: ${query}`);\n            // Check if the query is a valid URL\n            const urlPattern = /^https?:\\/\\//i;\n            if (urlPattern.test(query)) {\n                // Direct URL navigation with automation detection\n                console.log(`[Smart Browsing] 🌐 Navigating to URL with automation support: ${query}`);\n                if (!plan.visitedUrls.includes(query)) {\n                    plan.visitedUrls.push(query);\n                }\n                // Check if this requires complex automation\n                const requiresComplexAutomation = this.detectComplexAutomationNeeds(subtask, query);\n                if (requiresComplexAutomation) {\n                    console.log(`[Smart Browsing] 🤖 Using complex automation workflow for: ${query}`);\n                    return await this.executeComplexAutomation(subtask, query, plan);\n                } else {\n                    // Standard navigation with enhanced error handling\n                    const result = await this.browserlessService.navigateAndExtract(query);\n                    if (result.data) {\n                        console.log(`[Smart Browsing] ✅ Navigation successful for: ${query}`);\n                        return {\n                            success: true,\n                            data: result.data\n                        };\n                    } else {\n                        return {\n                            success: false,\n                            error: 'No data extracted from navigation'\n                        };\n                    }\n                }\n            } else {\n                // Not a direct URL - convert to search task with enhanced query processing\n                console.log(`[Smart Browsing] 🔄 Converting navigation to enhanced search: ${query}`);\n                // Extract meaningful search terms from the navigation description\n                let searchQuery = query;\n                if (query.toLowerCase().includes('navigate to')) {\n                    searchQuery = query.replace(/navigate to\\s*/i, '').trim();\n                }\n                if (query.toLowerCase().includes('websites of')) {\n                    searchQuery = searchQuery.replace(/websites of\\s*/i, '').trim();\n                }\n                // Add current year for better results\n                const currentYear = new Date().getFullYear();\n                if (!searchQuery.includes(currentYear.toString())) {\n                    searchQuery = `${searchQuery} ${currentYear}`;\n                }\n                // Use the enhanced search functionality\n                return await this.executeSearchSubtask({\n                    ...subtask,\n                    type: 'search',\n                    query: searchQuery,\n                    searchTerms: [\n                        searchQuery,\n                        `${searchQuery} latest`,\n                        `${searchQuery} official website`,\n                        `${searchQuery} information`\n                    ]\n                }, plan);\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Navigation failed';\n            console.log(`[Smart Browsing] ❌ Navigation error: ${errorMessage}`);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Detect if a subtask requires complex automation (forms, CAPTCHAs, etc.)\n   */ detectComplexAutomationNeeds(subtask, url) {\n        const description = (subtask.description || '').toLowerCase();\n        const query = (subtask.query || '').toLowerCase();\n        // Keywords that indicate complex automation needs\n        const complexKeywords = [\n            'form',\n            'submit',\n            'login',\n            'register',\n            'book',\n            'reserve',\n            'purchase',\n            'checkout',\n            'payment',\n            'captcha',\n            'verify',\n            'authenticate',\n            'sign in',\n            'sign up',\n            'create account',\n            'fill out',\n            'application',\n            'survey',\n            'reservation',\n            'booking',\n            'order'\n        ];\n        // URL patterns that often require complex automation\n        const complexUrlPatterns = [\n            'login',\n            'register',\n            'checkout',\n            'booking',\n            'reservation',\n            'form',\n            'application',\n            'survey',\n            'account',\n            'dashboard',\n            'admin',\n            'portal'\n        ];\n        return complexKeywords.some((keyword)=>description.includes(keyword) || query.includes(keyword)) || complexUrlPatterns.some((pattern)=>url.toLowerCase().includes(pattern));\n    }\n    /**\n   * Execute complex automation workflow using advanced BrowserQL capabilities\n   */ async executeComplexAutomation(subtask, url, plan) {\n        try {\n            console.log(`[Smart Browsing] 🤖 Starting complex automation for: ${url}`);\n            // Create a workflow based on the subtask requirements\n            const workflow = this.buildAutomationWorkflow(subtask, url);\n            const result = await this.browserlessService.executeComplexWorkflow(url, workflow);\n            if (result && result.data) {\n                console.log(`[Smart Browsing] ✅ Complex automation successful for: ${url}`);\n                // Store session info for potential follow-up tasks\n                if (result.sessionId && result.reconnectUrl) {\n                    plan.sessionInfo = {\n                        sessionId: result.sessionId,\n                        reconnectUrl: result.reconnectUrl,\n                        lastUsed: new Date()\n                    };\n                }\n                return {\n                    success: true,\n                    data: result.data\n                };\n            } else {\n                return {\n                    success: false,\n                    error: 'Complex automation completed but no data extracted'\n                };\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Complex automation failed';\n            console.log(`[Smart Browsing] ❌ Complex automation error: ${errorMessage}`);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Build automation workflow steps based on subtask requirements\n   */ buildAutomationWorkflow(subtask, url) {\n        const workflow = [];\n        const description = (subtask.description || '').toLowerCase();\n        const query = (subtask.query || '').toLowerCase();\n        // Always start with navigation\n        workflow.push({\n            name: 'navigate',\n            type: 'navigate',\n            params: {\n                url,\n                waitUntil: 'networkIdle'\n            }\n        });\n        // Add wait for page to stabilize\n        workflow.push({\n            name: 'wait_for_page',\n            type: 'wait',\n            params: {\n                time: 2000\n            }\n        });\n        // Handle CAPTCHA if likely to be present\n        if (description.includes('captcha') || description.includes('verify') || description.includes('challenge') || description.includes('protection')) {\n            workflow.push({\n                name: 'solve_captcha',\n                type: 'captcha',\n                params: {\n                    type: 'hcaptcha'\n                } // Default to hCaptcha, can detect others\n            });\n        }\n        // Handle form filling\n        if (description.includes('form') || description.includes('fill') || description.includes('submit') || description.includes('input')) {\n            workflow.push({\n                name: 'wait_for_form',\n                type: 'wait',\n                params: {\n                    time: 1000\n                }\n            });\n            // Extract form structure for analysis\n            workflow.push({\n                name: 'analyze_form',\n                type: 'extract',\n                params: {\n                    selector: 'form, input, textarea, select',\n                    type: 'html'\n                }\n            });\n        }\n        // Handle login/authentication\n        if (description.includes('login') || description.includes('sign in') || description.includes('authenticate') || description.includes('credentials')) {\n            workflow.push({\n                name: 'wait_for_login_form',\n                type: 'wait',\n                params: {\n                    time: 1500\n                }\n            });\n            workflow.push({\n                name: 'extract_login_form',\n                type: 'extract',\n                params: {\n                    selector: 'form[action*=\"login\"], form[id*=\"login\"], input[type=\"password\"]',\n                    type: 'html'\n                }\n            });\n        }\n        // Handle infinite scroll if needed\n        if (description.includes('scroll') || description.includes('load more') || description.includes('pagination') || description.includes('infinite')) {\n            workflow.push({\n                name: 'scroll_content',\n                type: 'scroll',\n                params: {\n                    direction: 'down',\n                    distance: 1000\n                }\n            });\n            workflow.push({\n                name: 'wait_after_scroll',\n                type: 'wait',\n                params: {\n                    time: 2000\n                }\n            });\n            // Repeat scroll if needed\n            workflow.push({\n                name: 'scroll_more',\n                type: 'scroll',\n                params: {\n                    direction: 'down',\n                    distance: 1000\n                }\n            });\n        }\n        // Handle booking/reservation specific workflows\n        if (description.includes('book') || description.includes('reserve') || description.includes('appointment') || description.includes('schedule')) {\n            workflow.push({\n                name: 'wait_for_booking_interface',\n                type: 'wait',\n                params: {\n                    time: 2000\n                }\n            });\n            workflow.push({\n                name: 'extract_booking_options',\n                type: 'extract',\n                params: {\n                    selector: '.booking, .reservation, .calendar, .schedule, [class*=\"book\"], [class*=\"reserve\"]',\n                    type: 'html'\n                }\n            });\n        }\n        // Always end with comprehensive content extraction\n        workflow.push({\n            name: 'extract_main_content',\n            type: 'extract',\n            params: {\n                selector: 'main, .main, .content, .container, body',\n                type: 'text'\n            }\n        });\n        // Extract structured data if available\n        workflow.push({\n            name: 'extract_structured_data',\n            type: 'extract',\n            params: {\n                selector: '[itemscope], [data-*], .product, .article, .post',\n                type: 'html'\n            }\n        });\n        // Take screenshot for debugging/verification\n        workflow.push({\n            name: 'take_screenshot',\n            type: 'screenshot',\n            params: {\n                fullPage: false\n            }\n        });\n        console.log(`[Smart Browsing] 🔧 Built automation workflow with ${workflow.length} steps for: ${url}`);\n        return workflow;\n    }\n    /**\n   * Execute extract subtask\n   */ async executeExtractSubtask(subtask, plan) {\n        // Similar to navigate but with specific extraction focus\n        return this.executeNavigateSubtask(subtask, plan);\n    }\n    /**\n   * Execute analyze subtask - synthesize gathered information\n   */ async executeAnalyzeSubtask(subtask, plan, browsingConfig) {\n        try {\n            console.log(`[Smart Browsing] 🧠 Analyzing gathered data...`);\n            // Collect all data from completed subtasks\n            const gatheredData = plan.subtasks.filter((task)=>task.status === 'completed' && task.result).map((task)=>({\n                    type: task.type,\n                    description: task.description,\n                    query: task.query,\n                    data: task.result\n                }));\n            if (gatheredData.length === 0) {\n                return {\n                    success: false,\n                    error: 'No data available for analysis'\n                };\n            }\n            // Use AI to analyze and synthesize the data\n            const model = browsingConfig.browsing_models[0];\n            if (!model) {\n                return {\n                    success: false,\n                    error: 'No AI model available for analysis'\n                };\n            }\n            const analysisPrompt = this.buildAnalysisPrompt(plan.originalQuery, gatheredData);\n            const aiResult = await this.callAIForAnalysis(analysisPrompt, model);\n            if (aiResult.success && aiResult.content) {\n                return {\n                    success: true,\n                    data: {\n                        analysis: aiResult.content,\n                        sourceData: gatheredData\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    error: aiResult.error || 'Analysis failed'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Analysis failed'\n            };\n        }\n    }\n    /**\n   * Calculate progress percentage\n   */ calculateProgress(plan) {\n        const totalTasks = plan.subtasks.length;\n        const completedTasks = plan.completedSubtasks.length;\n        return Math.round(completedTasks / totalTasks * 100);\n    }\n    /**\n   * Log progress update\n   */ logProgress(plan) {\n        const completed = plan.completedSubtasks.length;\n        const failed = plan.failedSubtasks.length;\n        const total = plan.subtasks.length;\n        const remaining = total - completed - failed;\n        console.log(`[Smart Browsing] 📊 Progress: ${plan.progress}% (${completed}/${total} completed, ${failed} failed, ${remaining} remaining)`);\n    }\n    /**\n   * Build analysis prompt for AI\n   */ buildAnalysisPrompt(originalQuery, gatheredData) {\n        // Get current date and time for context\n        const now = new Date();\n        const currentDateTime = now.toLocaleString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n            timeZoneName: 'short'\n        });\n        const dataContext = gatheredData.map((item, index)=>`Source ${index + 1} (${item.type}): ${item.description}\\nQuery: ${item.query}\\nData: ${JSON.stringify(item.data, null, 2)}`).join('\\n\\n---\\n\\n');\n        return `You are an expert information analyst. Analyze the following browsing data and provide a comprehensive answer to the original query.\n\nCURRENT DATE & TIME: ${currentDateTime}\nORIGINAL QUERY: \"${originalQuery}\"\n\nGATHERED DATA:\n${dataContext}\n\nPlease provide:\n1. A comprehensive answer to the original query\n2. Key findings and insights with temporal relevance\n3. Any conflicting information found\n4. Confidence level in the findings\n5. Recommendations for further research if needed\n6. Note the recency and relevance of the information found\n\nIMPORTANT: Consider the current date when analyzing the data. Prioritize recent information and note if any data appears outdated. For time-sensitive queries, emphasize the most current findings.\n\nFormat your response as a clear, well-structured analysis that directly addresses the user's query with temporal awareness.`;\n    }\n    /**\n   * Call AI for planning\n   */ async callAIForPlanning(prompt, model) {\n        try {\n            const effectiveModelId = this.getEffectiveModelId(model);\n            const messages = [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ];\n            let apiUrl;\n            let headers;\n            let body;\n            // Configure API call based on provider (same as BrowsingExecutionService)\n            switch(model.provider){\n                case 'openai':\n                    apiUrl = 'https://api.openai.com/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'google':\n                    apiUrl = 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'anthropic':\n                    apiUrl = 'https://api.anthropic.com/v1/messages';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'x-api-key': model.api_key,\n                        'anthropic-version': '2023-06-01'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                case 'openrouter':\n                    apiUrl = 'https://openrouter.ai/api/v1/chat/completions';\n                    headers = {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${model.api_key}`,\n                        'HTTP-Referer': 'https://roukey.online',\n                        'X-Title': 'RouKey'\n                    };\n                    body = {\n                        model: effectiveModelId,\n                        messages,\n                        temperature: 0.1,\n                        max_tokens: 1500\n                    };\n                    break;\n                default:\n                    throw new Error(`Unsupported provider for planning: ${model.provider}`);\n            }\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(body),\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            let content;\n            if (model.provider === 'anthropic') {\n                content = result.content?.[0]?.text;\n            } else {\n                content = result.choices?.[0]?.message?.content;\n            }\n            if (!content) {\n                throw new Error('No content returned from AI model');\n            }\n            return {\n                success: true,\n                content\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * Call AI for analysis (same as planning but different purpose)\n   */ async callAIForAnalysis(prompt, model) {\n        return this.callAIForPlanning(prompt, model); // Same implementation\n    }\n    /**\n   * Get effective model ID (same logic as BrowsingExecutionService)\n   */ getEffectiveModelId(model) {\n        if (model.provider.toLowerCase() === 'openrouter') {\n            return model.model;\n        }\n        const parts = model.model.split('/');\n        return parts.length > 1 ? parts[parts.length - 1] : model.model;\n    }\n    /**\n   * Parse plan from AI response\n   */ parsePlanFromAI(aiResponse, originalQuery) {\n        try {\n            // Try to extract JSON from the response\n            const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n            if (!jsonMatch) {\n                throw new Error('No JSON found in AI response');\n            }\n            const parsed = JSON.parse(jsonMatch[0]);\n            if (!parsed.goal || !parsed.subtasks || !Array.isArray(parsed.subtasks)) {\n                throw new Error('Invalid plan structure from AI');\n            }\n            // Convert AI subtasks to our format\n            const subtasks = parsed.subtasks.map((task, index)=>({\n                    id: task.id || `ai_task_${Date.now()}_${index}`,\n                    type: task.type || 'search',\n                    description: task.description || `Task ${index + 1}`,\n                    query: task.query || originalQuery,\n                    status: 'pending',\n                    priority: task.priority || 5,\n                    attempts: 0,\n                    maxAttempts: 3,\n                    searchTerms: task.searchTerms || [],\n                    expectedInfo: task.expectedInfo || ''\n                }));\n            return {\n                goal: parsed.goal,\n                subtasks\n            };\n        } catch (error) {\n            console.warn('[Smart Browsing] Failed to parse AI plan, using fallback:', error);\n            return {\n                goal: `Find information about: ${originalQuery}`,\n                subtasks: this.createFallbackPlan(originalQuery, 'search')\n            };\n        }\n    }\n    /**\n   * Synthesize final result from all gathered data\n   */ async synthesizeFinalResult(plan, browsingConfig) {\n        try {\n            // Find the analysis result if available\n            const analysisTask = plan.subtasks.find((task)=>task.type === 'analyze' && task.status === 'completed' && task.result);\n            if (analysisTask && analysisTask.result?.analysis) {\n                return analysisTask.result.analysis;\n            }\n            // If no analysis task, create a summary from all completed tasks\n            const completedTasks = plan.subtasks.filter((task)=>task.status === 'completed' && task.result);\n            if (completedTasks.length === 0) {\n                return `No information was successfully gathered for the query: \"${plan.originalQuery}\"`;\n            }\n            // Create a basic summary\n            let summary = `Based on browsing research for \"${plan.originalQuery}\":\\n\\n`;\n            completedTasks.forEach((task, index)=>{\n                summary += `${index + 1}. ${task.description}:\\n`;\n                if (task.result?.results && Array.isArray(task.result.results)) {\n                    task.result.results.slice(0, 3).forEach((result)=>{\n                        summary += `   • ${result.title || 'Result'}\\n`;\n                    });\n                } else if (typeof task.result === 'string') {\n                    summary += `   ${task.result.substring(0, 200)}...\\n`;\n                }\n                summary += '\\n';\n            });\n            return summary;\n        } catch (error) {\n            console.error('[Smart Browsing] Error synthesizing final result:', error);\n            return `Research completed for \"${plan.originalQuery}\" but encountered errors in synthesis. Please check the individual results.`;\n        }\n    }\n    /**\n   * Log the browsing plan for debugging\n   */ logPlan(plan) {\n        console.log(`[Smart Browsing] 📋 BROWSING PLAN:`);\n        console.log(`[Smart Browsing] Goal: ${plan.goal}`);\n        console.log(`[Smart Browsing] Subtasks:`);\n        plan.subtasks.forEach((subtask, index)=>{\n            console.log(`[Smart Browsing]   ${index + 1}. [${subtask.type.toUpperCase()}] ${subtask.description}`);\n            console.log(`[Smart Browsing]      Query: \"${subtask.query}\"`);\n            console.log(`[Smart Browsing]      Priority: ${subtask.priority}, Status: ${subtask.status}`);\n            if (subtask.searchTerms && subtask.searchTerms.length > 0) {\n                console.log(`[Smart Browsing]      Alt terms: ${subtask.searchTerms.join(', ')}`);\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/SmartBrowsingExecutor.ts\n");

/***/ })

};
;