// Browserless.io API service with key rotation
// Similar to Jina API key rotation system

interface BrowserlessResponse {
  data: any;
  type: string;
  error?: string;
}

interface BrowserlessConfig {
  timeout?: number;
  viewport?: {
    width: number;
    height: number;
  };
  userAgent?: string;
}

class BrowserlessService {
  private static instance: BrowserlessService;
  private apiKeys: string[] = [];
  private currentKeyIndex = 0;
  private keyUsageCount: Map<string, number> = new Map();
  private keyErrors: Map<string, number> = new Map();
  private readonly MAX_RETRIES = 3;
  private readonly ERROR_THRESHOLD = 5;
  private readonly ENDPOINT = 'https://production-sfo.browserless.io';

  constructor() {
    this.initializeKeys();
    this.testConnectivity();
  }

  static getInstance(): BrowserlessService {
    if (!BrowserlessService.instance) {
      BrowserlessService.instance = new BrowserlessService();
    }
    return BrowserlessService.instance;
  }

  private initializeKeys(): void {
    // Load all Browserless API keys from environment (similar to Jina pattern)
    this.apiKeys = [
      process.env.BROWSERLESS_API_KEY,
      process.env.BROWSERLESS_API_KEY_2,
      process.env.BROWSERLESS_API_KEY_3,
      process.env.BROWSERLESS_API_KEY_4,
      process.env.BROWSERLESS_API_KEY_5,
      process.env.BROWSERLESS_API_KEY_6,
      process.env.BROWSERLESS_API_KEY_7,
      process.env.BROWSERLESS_API_KEY_8,
      process.env.BROWSERLESS_API_KEY_9,
      process.env.BROWSERLESS_API_KEY_10,
    ].filter(Boolean) as string[];

    if (this.apiKeys.length === 0) {
      console.error('No Browserless API keys found in environment variables');
      return;
    }

    console.log(`[Browserless] Initialized with ${this.apiKeys.length} API keys`);

    // Initialize usage tracking
    this.apiKeys.forEach(key => {
      this.keyUsageCount.set(key, 0);
      this.keyErrors.set(key, 0);
    });
  }

  /**
   * Test connectivity to Browserless service
   */
  private async testConnectivity(): Promise<void> {
    if (this.apiKeys.length === 0) {
      console.warn('[Browserless] No API keys available for connectivity test');
      return;
    }

    try {
      const testKey = this.apiKeys[0];
      const testUrl = `${this.ENDPOINT}/function?token=${testKey}`;

      // Simple connectivity test with proper function format
      const testCode = `
        export default async function ({ page }) {
          return {
            status: "connectivity-test-success",
            timestamp: new Date().toISOString()
          };
        }
      `;

      const response = await fetch(testUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/javascript',
        },
        body: testCode,
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      if (response.ok) {
        const result = await response.text();
        console.log('[Browserless] ✅ Connectivity test successful:', result);
      } else {
        const errorText = await response.text();
        console.warn(`[Browserless] ⚠️ Connectivity test failed with status: ${response.status}`);
        console.warn(`[Browserless] Error details: ${errorText}`);
        console.warn(`[Browserless] Using key: ${testKey.substring(0, 8)}...`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.warn(`[Browserless] ⚠️ Connectivity test failed: ${errorMessage}`);

      // Check for specific network errors
      if (errorMessage.includes('ENOTFOUND')) {
        console.error('[Browserless] 🌐 DNS resolution failed - check if chrome.browserless.io is accessible');
      } else if (errorMessage.includes('ECONNRESET')) {
        console.error('[Browserless] 🔌 Connection reset - possible network or service issue');
      }
    }
  }

  private getNextApiKey(): string {
    if (this.apiKeys.length === 0) {
      throw new Error('No Browserless API keys available');
    }

    // Find the key with lowest usage and errors
    let bestKey = this.apiKeys[0];
    let bestScore = this.calculateKeyScore(bestKey);

    for (const key of this.apiKeys) {
      const score = this.calculateKeyScore(key);
      if (score < bestScore) {
        bestKey = key;
        bestScore = score;
      }
    }

    return bestKey;
  }

  private calculateKeyScore(key: string): number {
    const usage = this.keyUsageCount.get(key) || 0;
    const errors = this.keyErrors.get(key) || 0;
    // Higher score = worse key (more usage + more errors)
    return usage + (errors * 10);
  }

  private incrementKeyUsage(key: string): void {
    const currentUsage = this.keyUsageCount.get(key) || 0;
    this.keyUsageCount.set(key, currentUsage + 1);
  }

  private incrementKeyError(key: string): void {
    const currentErrors = this.keyErrors.get(key) || 0;
    this.keyErrors.set(key, currentErrors + 1);
  }

  private isKeyHealthy(key: string): boolean {
    const errors = this.keyErrors.get(key) || 0;
    return errors < this.ERROR_THRESHOLD;
  }

  private getHealthyKeys(): string[] {
    return this.apiKeys.filter(key => this.isKeyHealthy(key));
  }

  async executeFunction(
    code: string,
    context?: any,
    config?: BrowserlessConfig
  ): Promise<BrowserlessResponse> {
    const healthyKeys = this.getHealthyKeys();
    
    if (healthyKeys.length === 0) {
      // Reset error counts if all keys are unhealthy
      this.keyErrors.clear();
      this.apiKeys.forEach(key => this.keyErrors.set(key, 0));
      console.log('All Browserless keys were unhealthy, resetting error counts');
    }

    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.MAX_RETRIES; attempt++) {
      try {
        const apiKey = this.getNextApiKey();
        this.incrementKeyUsage(apiKey);

        const response = await this.makeRequest(apiKey, code, context, config);
        
        // Success - return the response
        return response;
      } catch (error) {
        lastError = error as Error;
        console.error(`Browserless attempt ${attempt + 1} failed:`, error);
        
        // If it's a rate limit or quota error, mark the key as having an error
        if (this.isRateLimitError(error as Error)) {
          const currentKey = this.getNextApiKey();
          this.incrementKeyError(currentKey);
        }
      }
    }

    throw lastError || new Error('All Browserless API attempts failed');
  }

  private async makeRequest(
    apiKey: string,
    code: string,
    context?: any,
    config?: BrowserlessConfig
  ): Promise<BrowserlessResponse> {
    const url = `${this.ENDPOINT}/function?token=${apiKey}`;

    const requestBody = context ? {
      code,
      context
    } : code;

    const headers = {
      'Content-Type': context ? 'application/json' : 'application/javascript',
      'User-Agent': config?.userAgent || 'RouKey-Browser-Agent/1.0'
    };

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: context ? JSON.stringify(requestBody) : code,
      signal: AbortSignal.timeout(config?.timeout || 30000)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Browserless API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    return result;
  }

  private isRateLimitError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('rate limit') || 
           message.includes('quota') || 
           message.includes('429') ||
           message.includes('too many requests');
  }

  // Convenience methods for common browser tasks
  async navigateAndExtract(url: string, selector?: string): Promise<any> {
    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });
        
        const title = await page.title();
        const content = ${selector ? 
          `await page.$eval("${selector}", el => el.textContent || el.innerText)` : 
          'await page.evaluate(() => document.body.innerText)'
        };
        
        return {
          data: {
            url: "${url}",
            title,
            content: content?.trim() || ""
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  async searchAndExtractUnblocked(query: string, searchEngine: string = 'google'): Promise<any> {
    const searchUrl = searchEngine === 'google'
      ? `https://www.google.com/search?q=${encodeURIComponent(query)}`
      : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;

    const healthyKeys = this.getHealthyKeys();

    if (healthyKeys.length === 0) {
      throw new Error('No healthy Browserless API keys available');
    }

    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.MAX_RETRIES; attempt++) {
      try {
        const apiKey = this.getNextApiKey();
        this.incrementKeyUsage(apiKey);

        const url = `${this.ENDPOINT}/unblock?token=${apiKey}`;

        const requestBody = {
          url: searchUrl,
          content: true,
          browserWSEndpoint: false,
          cookies: false,
          screenshot: false,
          waitForSelector: {
            selector: 'h3, .g h3, .LC20lb, .b_algo h2',
            timeout: 10000
          }
        };

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'RouKey-Browser-Agent/1.0'
          },
          body: JSON.stringify(requestBody),
          signal: AbortSignal.timeout(60000)
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Browserless unblock API error: ${response.status} - ${errorText}`);
        }

        const result = await response.json();

        if (result.content) {
          // Parse the HTML content to extract search results
          const searchResults = this.parseSearchResults(result.content, searchEngine, query);

          return {
            data: {
              query,
              searchEngine,
              results: searchResults,
              timestamp: new Date().toISOString(),
              debug: {
                pageTitle: 'Unblocked search',
                pageUrl: searchUrl,
                totalElements: searchResults.length,
                usedSelector: 'unblock-api',
                extractedCount: searchResults.length
              }
            },
            type: "application/json"
          };
        } else {
          throw new Error('No content returned from unblock API');
        }

      } catch (error) {
        lastError = error as Error;
        console.error(`Browserless unblock attempt ${attempt + 1} failed:`, error);

        if (this.isRateLimitError(error as Error)) {
          const currentKey = this.getNextApiKey();
          this.incrementKeyError(currentKey);
        }
      }
    }

    throw lastError || new Error('All Browserless unblock API attempts failed');
  }

  private parseSearchResults(htmlContent: string, searchEngine: string, query: string): any[] {
    // Enhanced parsing for search results with snippets/descriptions
    // This extracts titles, links, AND content snippets for better AI context

    const results: any[] = [];

    if (searchEngine === 'google') {
      // Google search result patterns - more comprehensive extraction
      // Look for the main search result containers
      const resultContainerRegex = /<div[^>]*class="[^"]*g[^"]*"[^>]*>(.*?)<\/div>/gis;
      const titleRegex = /<h3[^>]*>([^<]+)<\/h3>/i;
      const linkRegex = /<a[^>]*href="([^"]*)"[^>]*>/i;
      // Google snippets are usually in spans with specific classes or data attributes
      const snippetRegex = /<span[^>]*(?:class="[^"]*st[^"]*"|data-content-feature="[^"]*snippet[^"]*")[^>]*>([^<]+)<\/span>/i;
      // Alternative snippet patterns
      const altSnippetRegex = /<div[^>]*class="[^"]*VwiC3b[^"]*"[^>]*>([^<]+)<\/div>/i;
      const descSnippetRegex = /<span[^>]*class="[^"]*aCOpRe[^"]*"[^>]*>([^<]+)<\/span>/i;

      let containerMatch;
      while ((containerMatch = resultContainerRegex.exec(htmlContent)) !== null && results.length < 8) {
        const containerContent = containerMatch[1];

        const titleMatch = titleRegex.exec(containerContent);
        const linkMatch = linkRegex.exec(containerContent);

        if (titleMatch && linkMatch) {
          const title = titleMatch[1].trim();
          const link = linkMatch[1];

          // Skip Google internal URLs
          if (link.startsWith('http') && !link.includes('google.com') && !link.includes('/search?')) {
            // Try to extract snippet/description
            let snippet = '';

            const snippetMatch = snippetRegex.exec(containerContent) ||
                                altSnippetRegex.exec(containerContent) ||
                                descSnippetRegex.exec(containerContent);

            if (snippetMatch) {
              snippet = snippetMatch[1].trim();
              // Clean up HTML entities and extra whitespace
              snippet = snippet.replace(/&[^;]+;/g, ' ').replace(/\s+/g, ' ').trim();
            }

            // If no snippet found, try to extract any text content from the container
            if (!snippet) {
              const textContent = containerContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
              const words = textContent.split(' ');
              if (words.length > 10) {
                snippet = words.slice(0, 30).join(' ') + '...';
              }
            }

            results.push({
              title,
              link,
              snippet: snippet || 'No description available',
              searchEngine: 'google',
              query,
              timestamp: new Date().toISOString()
            });
          }
        }
      }

      // Fallback: If container-based parsing didn't work, use the old method but with snippet extraction
      if (results.length === 0) {
        const titleRegex = /<h3[^>]*>([^<]+)<\/h3>/gi;
        const linkRegex = /<a[^>]*href="([^"]*)"[^>]*>/gi;

        let titleMatch;
        let linkMatch;
        const titles: string[] = [];
        const links: string[] = [];

        // Extract titles
        while ((titleMatch = titleRegex.exec(htmlContent)) !== null) {
          titles.push(titleMatch[1].trim());
        }

        // Extract links (filter Google URLs)
        while ((linkMatch = linkRegex.exec(htmlContent)) !== null) {
          const link = linkMatch[1];
          if (link.startsWith('http') && !link.includes('google.com') && !link.includes('/search?')) {
            links.push(link);
          }
        }

        // Combine titles and links with basic snippet extraction
        const maxResults = Math.min(titles.length, links.length, 5);
        for (let i = 0; i < maxResults; i++) {
          if (titles[i] && links[i]) {
            results.push({
              title: titles[i],
              link: links[i],
              snippet: 'No description available',
              searchEngine: 'google',
              query,
              timestamp: new Date().toISOString()
            });
          }
        }
      }
    } else {
      // Bing search result patterns - enhanced with snippets
      const resultContainerRegex = /<li[^>]*class="[^"]*b_algo[^"]*"[^>]*>(.*?)<\/li>/gis;
      const titleRegex = /<h2[^>]*><a[^>]*href="([^"]*)"[^>]*>([^<]+)<\/a><\/h2>/i;
      const snippetRegex = /<p[^>]*>([^<]+)<\/p>/i;

      let containerMatch;
      while ((containerMatch = resultContainerRegex.exec(htmlContent)) !== null && results.length < 8) {
        const containerContent = containerMatch[1];

        const titleMatch = titleRegex.exec(containerContent);
        const snippetMatch = snippetRegex.exec(containerContent);

        if (titleMatch) {
          const link = titleMatch[1];
          const title = titleMatch[2].trim();
          const snippet = snippetMatch ? snippetMatch[1].trim() : 'No description available';

          results.push({
            title,
            link,
            snippet,
            searchEngine: 'bing',
            query,
            timestamp: new Date().toISOString()
          });
        }
      }

      // Fallback for Bing if container parsing fails
      if (results.length === 0) {
        const resultRegex = /<h2[^>]*><a[^>]*href="([^"]*)"[^>]*>([^<]+)<\/a><\/h2>/gi;

        let match;
        while ((match = resultRegex.exec(htmlContent)) !== null && results.length < 5) {
          results.push({
            title: match[2].trim(),
            link: match[1],
            snippet: 'No description available',
            searchEngine: 'bing',
            query,
            timestamp: new Date().toISOString()
          });
        }
      }
    }

    console.log(`[Browserless] Parsed ${results.length} search results with snippets for query: "${query}"`);
    return results;
  }

  async searchAndExtract(query: string, searchEngine: string = 'google'): Promise<any> {
    const searchUrl = searchEngine === 'google'
      ? `https://www.google.com/search?q=${encodeURIComponent(query)}`
      : `https://www.bing.com/search?q=${encodeURIComponent(query)}`;

    const code = `
      export default async function ({ page }) {
        // Set a realistic user agent and headers
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        await page.setExtraHTTPHeaders({
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        });

        await page.goto("${searchUrl}", { waitUntil: 'networkidle0' });

        // Set up CAPTCHA solving
        const cdp = await page.createCDPSession();

        // Check for CAPTCHA and solve if found
        let captchaFound = false;
        cdp.on('Browserless.captchaFound', () => {
          console.log('CAPTCHA detected on search page');
          captchaFound = true;
        });

        // Wait a moment to see if CAPTCHA is detected
        await new Promise(resolve => setTimeout(resolve, 2000));

        if (captchaFound) {
          console.log('Attempting to solve CAPTCHA...');
          try {
            const { solved, error } = await cdp.send('Browserless.solveCaptcha');
            console.log('CAPTCHA solving result:', { solved, error });

            if (solved) {
              console.log('CAPTCHA solved successfully');
              // Wait for page to reload after CAPTCHA solving
              await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 }).catch(() => {
                console.log('No navigation after CAPTCHA solve, continuing...');
              });
            } else {
              console.log('CAPTCHA solving failed:', error);
            }
          } catch (captchaError) {
            console.log('CAPTCHA solving error:', captchaError);
          }
        }

        // Wait for search results to load with multiple fallback selectors
        let resultsLoaded = false;
        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];
        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];

        const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;

        for (const selector of selectorsToTry) {
          try {
            await page.waitForSelector(selector, { timeout: 3000 });
            resultsLoaded = true;
            console.log('Found results with selector:', selector);
            break;
          } catch (e) {
            console.log('Selector failed:', selector);
            continue;
          }
        }

        if (!resultsLoaded) {
          // Give it one more chance with a longer timeout on the most common selector
          try {
            await page.waitForSelector('${searchEngine === 'google' ? 'h3' : '.b_algo'}', { timeout: 5000 });
          } catch (e) {
            console.log('All selectors failed, proceeding anyway...');
          }
        }

        const results = await page.evaluate(() => {
          console.log('Starting search results extraction...');

          // Try multiple selectors for extracting results
          const googleSelectors = [
            '[data-ved] h3',
            'h3',
            '.g h3',
            '.LC20lb',
            '.DKV0Md',
            '#search h3',
            '.yuRUbf h3',
            'a h3',
            '[role="heading"]'
          ];
          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];

          const selectorsToTry = '${searchEngine}' === 'google' ? googleSelectors : bingSelectors;
          let elements = [];
          let usedSelector = '';

          // Debug: Log page content
          console.log('Page title:', document.title);
          console.log('Page URL:', window.location.href);
          console.log('Page body preview:', document.body.innerText.substring(0, 500));

          for (const selector of selectorsToTry) {
            elements = document.querySelectorAll(selector);
            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');
            if (elements.length > 0) {
              usedSelector = selector;
              console.log('Found', elements.length, 'results with selector:', selector);
              break;
            }
          }

          // If no results found, try a more generic approach
          if (elements.length === 0) {
            console.log('No results with specific selectors, trying generic approach...');
            // Try to find any links that look like search results
            const allLinks = document.querySelectorAll('a[href*="/url?"]');
            console.log('Found', allLinks.length, 'Google result links');

            if (allLinks.length > 0) {
              elements = Array.from(allLinks).map(link => {
                const h3 = link.querySelector('h3');
                return h3 || link;
              }).filter(el => el && el.textContent?.trim());
              usedSelector = 'a[href*="/url?"] h3 (fallback)';
              console.log('Using fallback approach, found', elements.length, 'elements');
            }
          }

          const extractedResults = Array.from(elements).slice(0, 5).map(el => {
            const title = el.textContent?.trim() || '';
            let link = '';

            // Try to get the link
            if (el.href) {
              link = el.href;
            } else {
              const closestLink = el.closest('a');
              if (closestLink) {
                link = closestLink.href;
              }
            }

            return { title, link };
          }).filter(item => item.title && item.link);

          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);

          return {
            results: extractedResults,
            debug: {
              pageTitle: document.title,
              pageUrl: window.location.href,
              totalElements: elements.length,
              usedSelector: usedSelector || 'none',
              extractedCount: extractedResults.length
            }
          };
        });

        return {
          data: {
            query: "${query}",
            searchEngine: "${searchEngine}",
            results: results.results,
            timestamp: new Date().toISOString(),
            debug: results.debug
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code, null, {
      timeout: 60000 // Increase timeout for CAPTCHA solving
    });
  }

  async takeScreenshot(url: string, options?: {
    fullPage?: boolean;
    selector?: string;
    quality?: number;
  }): Promise<any> {
    const fullPage = options?.fullPage ?? false;
    const selector = options?.selector || '';
    const quality = options?.quality || 80;

    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        let screenshot;
        if ("${selector}") {
          // Screenshot specific element
          const element = await page.waitForSelector("${selector}", { timeout: 10000 });
          screenshot = await element.screenshot({
            encoding: 'base64',
            type: 'png'
          });
        } else {
          // Screenshot full page or viewport
          screenshot = await page.screenshot({
            encoding: 'base64',
            fullPage: ${fullPage},
            type: 'png',
            quality: ${quality}
          });
        }

        return {
          data: {
            url: "${url}",
            screenshot: screenshot,
            selector: "${selector}",
            fullPage: ${fullPage},
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  /**
   * Advanced form filling with intelligent field detection
   */
  async fillForm(url: string, formData: Record<string, any>, options?: {
    submitAfterFill?: boolean;
    waitForNavigation?: boolean;
    formSelector?: string;
  }): Promise<any> {
    const submitAfterFill = options?.submitAfterFill ?? false;
    const waitForNavigation = options?.waitForNavigation ?? false;
    const formSelector = options?.formSelector || 'form';

    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        const formData = ${JSON.stringify(formData)};
        const results = [];

        // Wait for form to be present
        await page.waitForSelector("${formSelector}", { timeout: 10000 });

        // Fill each field intelligently
        for (const [fieldName, value] of Object.entries(formData)) {
          try {
            // Try multiple selector strategies
            const selectors = [
              \`input[name="\${fieldName}"]\`,
              \`input[id="\${fieldName}"]\`,
              \`textarea[name="\${fieldName}"]\`,
              \`select[name="\${fieldName}"]\`,
              \`input[placeholder*="\${fieldName}"]\`,
              \`input[aria-label*="\${fieldName}"]\`,
              \`[data-testid="\${fieldName}"]\`
            ];

            let filled = false;
            for (const selector of selectors) {
              const elements = await page.$$(selector);
              if (elements.length > 0) {
                const element = elements[0];
                const tagName = await element.evaluate(el => el.tagName.toLowerCase());

                if (tagName === 'select') {
                  await element.selectOption(value.toString());
                } else if (tagName === 'input') {
                  const inputType = await element.getAttribute('type');
                  if (inputType === 'checkbox' || inputType === 'radio') {
                    if (value) await element.check();
                  } else {
                    await element.fill(value.toString());
                  }
                } else {
                  await element.fill(value.toString());
                }

                results.push({
                  field: fieldName,
                  selector: selector,
                  value: value,
                  success: true
                });
                filled = true;
                break;
              }
            }

            if (!filled) {
              results.push({
                field: fieldName,
                value: value,
                success: false,
                error: 'Field not found'
              });
            }
          } catch (error) {
            results.push({
              field: fieldName,
              value: value,
              success: false,
              error: error.message
            });
          }
        }

        let submitResult = null;
        if (${submitAfterFill}) {
          try {
            const submitButton = await page.$('input[type="submit"], button[type="submit"], button:has-text("Submit")');
            if (submitButton) {
              ${waitForNavigation ? 'await Promise.all([page.waitForNavigation(), submitButton.click()]);' : 'await submitButton.click();'}
              submitResult = { success: true, message: 'Form submitted successfully' };
            } else {
              submitResult = { success: false, error: 'Submit button not found' };
            }
          } catch (error) {
            submitResult = { success: false, error: error.message };
          }
        }

        return {
          data: {
            url: "${url}",
            formFillResults: results,
            submitResult: submitResult,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  /**
   * CAPTCHA solving with multiple strategies
   */
  async solveCaptcha(url: string, captchaType: 'recaptcha' | 'hcaptcha' | 'text' = 'recaptcha'): Promise<any> {
    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        const captchaType = "${captchaType}";
        let result = { success: false, type: captchaType };

        try {
          if (captchaType === 'recaptcha') {
            // Look for reCAPTCHA
            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');
            if (recaptcha) {
              // For now, we'll detect and report the presence
              // In production, integrate with 2captcha or similar service
              const sitekey = await recaptcha.getAttribute('data-sitekey');
              result = {
                success: false,
                type: 'recaptcha',
                detected: true,
                sitekey: sitekey,
                message: 'reCAPTCHA detected but solving not implemented yet'
              };
            }
          } else if (captchaType === 'hcaptcha') {
            // Look for hCaptcha
            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');
            if (hcaptcha) {
              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');
              result = {
                success: false,
                type: 'hcaptcha',
                detected: true,
                sitekey: sitekey,
                message: 'hCaptcha detected but solving not implemented yet'
              };
            }
          } else if (captchaType === 'text') {
            // Look for text-based CAPTCHA
            const textCaptcha = await page.$('img[src*="captcha"], img[alt*="captcha"], .captcha-image');
            if (textCaptcha) {
              result = {
                success: false,
                type: 'text',
                detected: true,
                message: 'Text CAPTCHA detected but solving not implemented yet'
              };
            }
          }

          // If no CAPTCHA detected
          if (!result.detected) {
            result = {
              success: true,
              type: captchaType,
              detected: false,
              message: 'No CAPTCHA detected on page'
            };
          }
        } catch (error) {
          result = {
            success: false,
            type: captchaType,
            error: error.message
          };
        }

        return {
          data: {
            url: "${url}",
            captchaResult: result,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  /**
   * Execute custom JavaScript with advanced capabilities
   */
  async executeAdvancedScript(url: string, script: string, options?: {
    waitForSelector?: string;
    timeout?: number;
    returnType?: 'json' | 'text' | 'screenshot';
  }): Promise<any> {
    const waitForSelector = options?.waitForSelector || '';
    const timeout = options?.timeout || 30000;
    const returnType = options?.returnType || 'json';

    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        ${waitForSelector ? `await page.waitForSelector("${waitForSelector}", { timeout: ${timeout} });` : ''}

        // Execute custom script
        const scriptResult = await page.evaluate(() => {
          ${script}
        });

        let finalResult = scriptResult;

        if ("${returnType}" === 'screenshot') {
          const screenshot = await page.screenshot({
            encoding: 'base64',
            type: 'png'
          });
          finalResult = {
            scriptResult: scriptResult,
            screenshot: screenshot
          };
        }

        return {
          data: {
            url: "${url}",
            result: finalResult,
            returnType: "${returnType}",
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  /**
   * Smart content extraction with multiple strategies
   */
  async smartExtract(url: string, extractionGoals: string[]): Promise<any> {
    const code = `
      export default async function ({ page }) {
        await page.goto("${url}", { waitUntil: 'networkidle0' });

        const goals = ${JSON.stringify(extractionGoals)};
        const results = {};

        // Common extraction patterns
        const extractors = {
          prices: () => {
            const priceSelectors = [
              '[class*="price"]', '[id*="price"]', '.cost', '.amount',
              '[data-testid*="price"]', '.currency', '[class*="dollar"]'
            ];
            const prices = [];
            priceSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                const text = el.textContent?.trim();
                if (text && /[$£€¥₹]|\\d+\\.\\d{2}/.test(text)) {
                  prices.push({
                    text: text,
                    selector: selector,
                    element: el.tagName
                  });
                }
              });
            });
            return prices;
          },

          contact: () => {
            const contactSelectors = [
              '[href^="mailto:"]', '[href^="tel:"]', '.contact', '.email', '.phone'
            ];
            const contacts = [];
            contactSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                contacts.push({
                  text: el.textContent?.trim(),
                  href: el.getAttribute('href'),
                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'
                });
              });
            });
            return contacts;
          },

          products: () => {
            const productSelectors = [
              '.product', '[class*="product"]', '.item', '[data-testid*="product"]'
            ];
            const products = [];
            productSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                const title = el.querySelector('h1, h2, h3, .title, [class*="title"]')?.textContent?.trim();
                const price = el.querySelector('[class*="price"], .cost')?.textContent?.trim();
                const image = el.querySelector('img')?.src;
                if (title) {
                  products.push({ title, price, image });
                }
              });
            });
            return products;
          },

          text: () => {
            // Extract main content
            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];
            let content = '';
            for (const selector of contentSelectors) {
              const el = document.querySelector(selector);
              if (el) {
                content = el.textContent?.trim() || '';
                break;
              }
            }
            if (!content) {
              content = document.body.textContent?.trim() || '';
            }
            return content.substring(0, 5000); // Limit to 5000 chars
          },

          links: () => {
            const links = [];
            document.querySelectorAll('a[href]').forEach(el => {
              const href = el.getAttribute('href');
              const text = el.textContent?.trim();
              if (href && text && !href.startsWith('#')) {
                links.push({
                  url: new URL(href, window.location.href).href,
                  text: text
                });
              }
            });
            return links.slice(0, 50); // Limit to 50 links
          }
        };

        // Execute extractors based on goals
        goals.forEach(goal => {
          const goalLower = goal.toLowerCase();
          if (goalLower.includes('price') || goalLower.includes('cost')) {
            results.prices = extractors.prices();
          }
          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {
            results.contact = extractors.contact();
          }
          if (goalLower.includes('product') || goalLower.includes('item')) {
            results.products = extractors.products();
          }
          if (goalLower.includes('text') || goalLower.includes('content')) {
            results.text = extractors.text();
          }
          if (goalLower.includes('link') || goalLower.includes('url')) {
            results.links = extractors.links();
          }
        });

        // If no specific goals, extract everything
        if (goals.length === 0) {
          Object.keys(extractors).forEach(key => {
            results[key] = extractors[key]();
          });
        }

        return {
          data: {
            url: "${url}",
            extractionGoals: goals,
            results: results,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;

    return this.executeFunction(code);
  }

  // Get service statistics
  getStats(): any {
    return {
      totalKeys: this.apiKeys.length,
      healthyKeys: this.getHealthyKeys().length,
      keyUsage: Object.fromEntries(this.keyUsageCount),
      keyErrors: Object.fromEntries(this.keyErrors)
    };
  }
}

export default BrowserlessService;
